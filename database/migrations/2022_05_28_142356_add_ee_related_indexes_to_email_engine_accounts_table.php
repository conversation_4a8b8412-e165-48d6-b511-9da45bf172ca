<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEeRelatedIndexesToEmailEngineAccountsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('email_engine_accounts', function (Blueprint $table) {
            $table->index(['account_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('email_engine_accounts', function (Blueprint $table) {
            $table->dropIndex(['account_id']);
        });
    }
}
