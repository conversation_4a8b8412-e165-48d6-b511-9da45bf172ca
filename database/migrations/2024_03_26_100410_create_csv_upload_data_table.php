<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('csv_upload_data', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('csv_upload_id');
            $table->string('status')->nullable();
            $table->string('error')->nullable();
            $table->json('row_data');
            $table->timestamps();

            $table->foreign('csv_upload_id')
                ->references('id')
                ->on('csv_uploads')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('csv_upload_data');
    }
};
