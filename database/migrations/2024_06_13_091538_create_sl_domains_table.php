<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sl_domains', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
//            $table->string('title')->nullable();
            $table->unsignedBigInteger('week_id')->nullable()->index();
            $table->unsignedBigInteger('month_id')->nullable()->index();
            $table->unsignedSmallInteger('platform_id')->nullable()->index();
            $table->unsignedSmallInteger('status_id')->nullable()->index();
            $table->unsignedSmallInteger('employee_count_id')->nullable()->index();
            $table->unsignedSmallInteger('estimated_sales_id')->nullable()->index();
            $table->unsignedSmallInteger('plan_id')->nullable()->index();
            $table->unsignedSmallInteger('product_count_id')->nullable()->index();
            $table->unsignedSmallInteger('region_id')->nullable()->index();
            $table->unsignedBigInteger('country_id')->nullable()->index();
            $table->unsignedBigInteger('currency_id')->nullable()->index();
            $table->unsignedBigInteger('language_id')->nullable()->index();
            $table->unsignedSmallInteger('domain_type_id')->nullable()->index();
            $table->unsignedBigInteger('top_level_domain_id')->nullable()->index();
            $table->unsignedBigInteger('theme_vendor_id')->nullable()->index();
            $table->unsignedBigInteger('theme_id')->nullable()->index();
            $table->json('data')->nullable();
            $table->timestamps();
        });

        Schema::create('sl_contact_type_domain', function (Blueprint $table) {
            $table->unsignedBigInteger('contact_type_id');
            $table->unsignedBigInteger('domain_id');

            $table->foreign('contact_type_id')->references('id')->on('sl_contact_types')->onDelete('cascade');
            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
        });

        Schema::create('sl_category_domain', function (Blueprint $table) {
            $table->unsignedBigInteger('category_id');
            $table->unsignedBigInteger('domain_id');

            $table->foreign('category_id')->references('id')->on('sl_categories')->onDelete('cascade');
            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
        });

        Schema::create('sl_app_domain', function (Blueprint $table) {
            $table->unsignedBigInteger('app_id');
            $table->unsignedBigInteger('domain_id');

            $table->foreign('app_id')->references('id')->on('sl_apps')->onDelete('cascade');
            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
        });

        Schema::create('sl_domain_technology', function (Blueprint $table) {
            $table->unsignedBigInteger('domain_id');
            $table->unsignedBigInteger('technology_id');

            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
            $table->foreign('technology_id')->references('id')->on('sl_technologies')->onDelete('cascade');
        });

        Schema::create('sl_domain_phone_country_code', function (Blueprint $table) {
            $table->unsignedBigInteger('domain_id');
            $table->unsignedBigInteger('phone_country_code_id');

            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
            $table->foreign('phone_country_code_id', 'sl_phone_country_code_foreign')->references('id')->on('sl_phone_country_codes')->onDelete('cascade');
        });

        Schema::create('sl_domain_feature', function (Blueprint $table) {
            $table->unsignedBigInteger('domain_id');
            $table->unsignedBigInteger('feature_id');

            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
            $table->foreign('feature_id')->references('id')->on('sl_features')->onDelete('cascade');
        });

        Schema::create('sl_domain_tag', function (Blueprint $table) {
            $table->unsignedBigInteger('domain_id');
            $table->unsignedBigInteger('tag_id');

            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
            $table->foreign('tag_id')->references('id')->on('sl_tags')->onDelete('cascade');
        });

        Schema::create('sl_domain_sales_channel', function (Blueprint $table) {
            $table->unsignedBigInteger('domain_id');
            $table->unsignedBigInteger('sales_channel_id');

            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
            $table->foreign('sales_channel_id', 'sl_sales_channel_foreign')->references('id')->on('sl_sales_channels')->onDelete('cascade');
        });

        Schema::create('sl_domain_shipping_country', function (Blueprint $table) {
            $table->unsignedBigInteger('domain_id');
            $table->unsignedBigInteger('shipping_country_id');

            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
            $table->foreign('shipping_country_id', 'sl_shipping_country_foreign')->references('id')->on('sl_shipping_countries')->onDelete('cascade');
        });

        Schema::create('sl_domain_shipping_carrier', function (Blueprint $table) {
            $table->unsignedBigInteger('domain_id');
            $table->unsignedBigInteger('shipping_carrier_id');

            $table->foreign('domain_id')->references('id')->on('sl_domains')->onDelete('cascade');
            $table->foreign('shipping_carrier_id', 'sl_shipping_carrier_foreign')->references('id')->on('sl_shipping_carriers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sl_category_domain');
        Schema::dropIfExists('sl_contact_type_domain');
        Schema::dropIfExists('sl_app_domain');
        Schema::dropIfExists('sl_domain_technology');
        Schema::dropIfExists('sl_domain_phone_country_code');
        Schema::dropIfExists('sl_domain_feature');
        Schema::dropIfExists('sl_domain_tag');
        Schema::dropIfExists('sl_domain_sales_channel');
        Schema::dropIfExists('sl_domain_shipping_country');
        Schema::dropIfExists('sl_domain_shipping_carrier');
        Schema::dropIfExists('sl_domains');
    }
};
