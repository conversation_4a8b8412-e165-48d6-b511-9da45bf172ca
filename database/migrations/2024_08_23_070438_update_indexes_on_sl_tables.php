<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sl_categories', function (Blueprint $table) {
            $table->index([DB::raw('domain_count desc')], 'sl_categories_domain_count_desc');
        });

        Schema::table('sl_category_domain', function (Blueprint $table) {
            $table->index('domain_id');
        });

        Schema::table('sl_apps', function (Blueprint $table) {
            $table->unsignedInteger('domain_count')->default(0);
            $table->index([DB::raw('domain_count desc')], 'sl_apps_domain_count_desc');
        });

        Schema::table('sl_contact_type_domain', function (Blueprint $table) {
            $table->index('domain_id');
        });

        Schema::table('sl_countries', function (Blueprint $table) {
            $table->index([DB::raw('domain_count desc')], 'sl_countries_domain_count_desc');
        });

        Schema::table('sl_currencies', function (Blueprint $table) {
            $table->index([DB::raw('domain_count desc')], 'sl_currencies_domain_count_desc');
        });

        Schema::table('sl_domain_data', function (Blueprint $table) {
            $table->unsignedInteger('id')->change();
        });

        Schema::table('sl_features', function (Blueprint $table) {
            $table->unsignedInteger('domain_count')->default(0);
            $table->index([DB::raw('domain_count desc')], 'sl_features_domain_count_desc');
        });

        Schema::table('sl_domain_feature', function (Blueprint $table) {
            $table->index('feature_id');
        });

        Schema::table('sl_sales_channels', function (Blueprint $table) {
            $table->unsignedInteger('domain_count')->default(0);
            $table->index([DB::raw('domain_count desc')], 'sl_sales_channels_domain_count_desc');
        });

        Schema::table('sl_domain_sales_channel', function (Blueprint $table) {
            $table->index('sales_channel_id');
        });

        Schema::table('sl_shipping_carriers', function (Blueprint $table) {
            $table->unsignedInteger('domain_count')->default(0);
            $table->index([DB::raw('domain_count desc')], 'sl_shipping_carriers_domain_count_desc');
        });

        Schema::table('sl_domain_shipping_carrier', function (Blueprint $table) {
            $table->index('shipping_carrier_id');
        });

        Schema::table('sl_shipping_countries', function (Blueprint $table) {
            $table->unsignedInteger('domain_count')->default(0);
            $table->index([DB::raw('domain_count desc')], 'sl_shipping_countries_domain_count_desc');
        });

        Schema::table('sl_domain_shipping_country', function (Blueprint $table) {
            $table->index('shipping_country_id');
        });

        Schema::table('sl_tags', function (Blueprint $table) {
            $table->unsignedInteger('domain_count')->default(0);
            $table->index([DB::raw('domain_count desc')], 'sl_tags_domain_count_desc');
        });

        Schema::table('sl_domain_tag', function (Blueprint $table) {
            $table->index('tag_id');
        });

        Schema::table('sl_technologies', function (Blueprint $table) {
            $table->unsignedInteger('domain_count')->default(0);
            $table->index([DB::raw('domain_count desc')], 'sl_technologies_domain_count_desc');
        });

        Schema::table('sl_domain_technology', function (Blueprint $table) {
            $table->index('technology_id');
        });

        Schema::table('sl_languages', function (Blueprint $table) {
            $table->index([DB::raw('domain_count desc')], 'sl_languages_domain_count_desc');
        });

        Schema::table('sl_months', function (Blueprint $table) {
            $table->index([DB::raw('name desc')], 'sl_months_name_desc');
        });

        Schema::table('sl_platforms', function (Blueprint $table) {
            $table->index('name');
            $table->index([DB::raw('domain_count desc')], 'sl_platforms_domain_count_desc');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sl_categories', function (Blueprint $table) {
            $table->dropIndex('sl_categories_domain_count_desc');
        });

        Schema::table('sl_category_domain', function (Blueprint $table) {
            $table->dropIndex(['domain_id']);
        });

        Schema::table('sl_apps', function (Blueprint $table) {
            $table->dropIndex('sl_apps_domain_count_desc');
            $table->dropColumn('domain_count');
        });

        Schema::table('sl_contact_type_domain', function (Blueprint $table) {
            $table->dropIndex(['domain_id']);
        });

        Schema::table('sl_countries', function (Blueprint $table) {
            $table->dropIndex('sl_countries_domain_count_desc');
        });

        Schema::table('sl_currencies', function (Blueprint $table) {
            $table->dropIndex('sl_currencies_domain_count_desc');
        });

        Schema::table('sl_features', function (Blueprint $table) {
            $table->dropIndex('sl_features_domain_count_desc');
            $table->dropColumn('domain_count');
        });

        Schema::table('sl_domain_feature', function (Blueprint $table) {
            $table->dropIndex(['feature_id']);
        });

        Schema::table('sl_sales_channels', function (Blueprint $table) {
            $table->dropIndex('sl_sales_channels_domain_count_desc');
            $table->dropColumn('domain_count');
        });

        Schema::table('sl_domain_sales_channel', function (Blueprint $table) {
            $table->dropIndex(['sales_channel_id']);
        });

        Schema::table('sl_shipping_carriers', function (Blueprint $table) {
            $table->dropIndex('sl_shipping_carriers_domain_count_desc');
            $table->dropColumn('domain_count');
        });

        Schema::table('sl_domain_shipping_carrier', function (Blueprint $table) {
            $table->dropIndex(['shipping_carrier_id']);
        });

        Schema::table('sl_shipping_countries', function (Blueprint $table) {
            $table->dropIndex('sl_shipping_countries_domain_count_desc');
            $table->dropColumn('domain_count');
        });

        Schema::table('sl_domain_shipping_country', function (Blueprint $table) {
            $table->dropIndex(['shipping_country_id']);
        });

        Schema::table('sl_tags', function (Blueprint $table) {
            $table->dropIndex('sl_tags_domain_count_desc');
            $table->dropColumn('domain_count');
        });

        Schema::table('sl_domain_tag', function (Blueprint $table) {
            $table->dropIndex(['tag_id']);
        });

        Schema::table('sl_technologies', function (Blueprint $table) {
            $table->dropIndex('sl_technologies_domain_count_desc');
            $table->dropColumn('domain_count');
        });

        Schema::table('sl_domain_technology', function (Blueprint $table) {
            $table->dropIndex(['technology_id']);
        });

        Schema::table('sl_languages', function (Blueprint $table) {
            $table->dropIndex('sl_languages_domain_count_desc');
        });

        Schema::table('sl_months', function (Blueprint $table) {
            $table->dropIndex('sl_months_name_desc');
        });

        Schema::table('sl_platforms', function (Blueprint $table) {
            $table->dropIndex('sl_platforms_domain_count_desc');
            $table->dropIndex(['name']);
        });
    }
};
