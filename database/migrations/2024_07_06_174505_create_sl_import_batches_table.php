<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sl_import_batches', function (Blueprint $table) {
            $table->id();
            $table->unsignedSmallInteger('batch_id')->unique();
            $table->unsignedInteger('start_row');
            $table->unsignedInteger('next_start_row')->nullable();
            $table->boolean('is_complete')->default(false);
            $table->dateTime('job_started_at')->nullable();
            $table->dateTime('job_ended_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sl_import_batches');
    }
};
