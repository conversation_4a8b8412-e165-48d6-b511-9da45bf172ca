<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sl_platform_hosts', function (Blueprint $table) {
            $table->string('registrable')->primary();
            $table->unsignedInteger('count_subdomains');
            $table->unsignedSmallInteger('confidence')->default(0)->index();
            $table->boolean('is_platform')->default(false)->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sl_platform_hosts');
    }
};
