<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sl_platform_hosts', function (Blueprint $table) {
            $table->boolean('is_platform')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sl_platform_hosts', function (Blueprint $table) {
            $table->boolean('is_platform')->default(false)->change();
        });
    }
};
