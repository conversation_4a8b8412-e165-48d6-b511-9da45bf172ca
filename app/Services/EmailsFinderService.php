<?php

namespace App\Services;

use App\Exceptions\ExternalServiceBillingException;
use App\Exceptions\ExternalServiceErrorException;
use App\Exceptions\EmailEnrichmentException;
use App\Models\ApiUsageLog;
use App\Models\ExternalService;
use App\Role;
use App\EmailNotification;
use App\Models\StoreLeads\Domain;
use Carbon\Carbon;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Exception;
use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Mail\ErrorNotification;

class EmailsFinderService
{
    private $domain;
    private $searchId;
    private $enableEnrichmentApis;
    private $acceptUnverified = false;
    private $sources = [
        'tomba',
        'anymail_finder',
    ];
    protected $externalServiceHandler;

    protected const DB_READ_CONNECTION = 'mysql::read';

    public function __construct(ExternalServiceHandler $externalServiceHandler, $enableEnrichmentApis = true)
    {
        $this->enableEnrichmentApis = $enableEnrichmentApis;
        $this->externalServiceHandler = $externalServiceHandler;
    }

    /**
     * Set the domain to find emails for.
     *
     * @param  Domain  $domain
     * @return $this
     */
    public function setDomain(Domain $domain)
    {
        $this->domain = $domain;
    }

    public function setSearchId($searchId)
    {
        $this->searchId = $searchId;
    }

    /**
     * Find (and enrich) emails for the domain.
     *
     * The method searches email addresses for the domain using available sources.
     * It returns an array with the found emails, their confidence and search status.
     * The status can be one of the following:
     * - not_found: No emails were found.
     * - verified: At least one verified email was found.
     * - apikey_errors: All sources had API key errors.
     * - retry_errors: All sources had retry errors.
     * - error: An unexpected error occurred.
     * The enrichment is done through EmailEnrichmentService.
     *
     * @return array
     */
    public function searchEmails()
    {
        $this->domain->load(['domainData']);

        $sourceApiKeyErrors = 0;
        $sourceRetryErrors = 0;
        $result = [
            "emails" => [],
            "confidence" => null, // 'verified' , 'not_verified'
            "status_code" => null, // 200, 400, 401, 402, 403, 404, 410, 418
            "status" => "not_found", // 'not_found', 'verified', 'not_verified', 'retry', 'billing_error', 'apikey_error', 'unhandled_error'
            "source" => null // 'tomba', 'anymail_finder'
        ];

        $availableSources = $this->getAvailableSources();

        // stop if all sources are not active and notify admin
        if (empty($availableSources)) {
            $this->logError("EmailsFinderService no active source");

            $notificationMessage = 'EmailsFinderService no active source. AnymailFinder and Tomba are inactive, add credits and set settings to active';
            $notificationUsers = Role::where('name', 'admin')->first()->users;
            Mail::to($notificationUsers)
                ->send(new ErrorNotification($notificationMessage, "EmailsFinderService no active source."));
            EmailNotification::create([
                'origin' => "EmailsFinderService ErrorNotification",
                'subject' => "EmailsFinderService no active source",
                'recipients' => 'admins',
            ]);

            $result["status"] = "apikey_errors";
            return $result;
        }

        $enrichmentService = app()->make(EmailEnrichmentService::class, [
            'domain' => $this->domain,
            'searchId' => $this->searchId,
            'enableEnrichmentApis' => $this->enableEnrichmentApis
        ]);
        if (!$enrichmentService->hasAvailableSources()) {
            $this->logError("EmailsFinderService no active enrichment source");
            $result["status"] = "apikey_errors";

            return $result;
        }

        foreach ($availableSources as $source) {
            $this->logInfo("search starting for: ".$source);

            switch ($source) {
                case 'tomba':
                    $searchResult = $this->searchTomba();
                    break;
                case 'anymail_finder':
                    $searchResult = $this->searchAnymailFinder();
                    break;
                default:
                    $searchResult = [
                        "emails" => [],
                        "confidence" => null,
                        "status_code" => null,
                        "status" => "error"
                    ];
                    break;
            }

            // $this->logInfo($searchResult);
            $this->logInfo("search end for: ".$source);

            // exit if got the verified emails
            if (!empty($searchResult['emails'])) {
                if ($searchResult['status'] == 'verified') {
                    $this->logInfo("Found " . count($searchResult['emails']) . ' verified emails');
                    $result = $searchResult;
                    break;
                } else {
                    // Not verified but found emails
                    $result['emails'] = array_merge($result['emails'], $searchResult['emails']);
                    $result['confidence'] = $result['confidence'] == 'verified' ? $result['confidence'] : $searchResult['confidence'];
                    $result['status'] = $result['status'] == 'verified' ? $result['status'] : $searchResult['status'];
                    $result['status_code'] = $result['status_code'] == 200 ? $result['status_code'] : $searchResult['status_code'];
                    $result['source'] = $result['confidence'] == 'verified' ? $result['source'] : $searchResult['source'];
                }
            }

            if (in_array($searchResult['status'], ["apikey_error", "billing_error"])) {
                $sourceApiKeyErrors++;

            } elseif (in_array($searchResult['status'], ["retry", "unhandled_error"])) {
                $sourceRetryErrors++;

            }
        }

        // let consumer know that no API is working
        if ($sourceApiKeyErrors == count($availableSources)) {
            $result["status"] = "apikey_errors";

            return $result;
        }

        // let consumer know that all APIs are busy or temporary available
        if (($sourceRetryErrors == count($availableSources)) || ($sourceApiKeyErrors + $sourceRetryErrors == count($availableSources))) {
            $result["status"] = "retry_errors";

            return $result;
        }

        // Enrich emails without info
        // Parse all the emails of a domain to make it "enriched".
        // Only apply the full flow (enrichmentService->enrichEmails) once per domain. Running more than once if not successful is a waste of credits.
        // So, run the full flow once and then just google search (enrichmentService->searchGoogle) the rest.
        // On Tomba, if we already have an email containing a full name then we don't need to run any flow on it, and we also consider that we have run the full flow,
        // so we only run google search on the rest of the emails.
        $enrichedEmails = [];
//        $isFullFlowRun = false;

        try {
            foreach ($result['emails'] as $emailData) {
                if ($result['source'] == 'tomba' && !empty($emailData['full_name']) && !empty($emailData['position'])) {
                    // If source is Tomba and email has full name and position, consider it enriched and add to result
                    $enrichedEmails[] = $emailData;
//                    $isFullFlowRun = true;
//                } elseif (!$isFullFlowRun) {
//                    // Run full enrichment flow if it hasn't been run yet
//                    $flowEnriched = $enrichmentService->enrichEmails([$emailData]);
//
//                    if (!empty($flowEnriched)) {
//                        $enrichedEmails[] = $flowEnriched[0];
//                    }
//                    $isFullFlowRun = true;
                } else {
                    // Run Google search for remaining emails
                    $googleEnriched = $enrichmentService->searchGoogle($emailData);
                    $enrichedEmails[] = $googleEnriched;
                }
            }
        } catch(ExternalServiceBillingException $e) {
            $result['status'] = 'apikey_errors';

            return $result;
        } catch(ExternalServiceErrorException $e) {
            $result['status'] = 'retry_errors';

            return $result;
        } catch (Exception $e) {
            $this->logError("Error in EnrichEmails: " . $e->getMessage());
            $result['status'] = 'retry_errors';

            return $result;
        }

        // $result['emails'] = $this->completeEmailProfiles($enrichedEmails);
        $result['emails'] = $enrichedEmails;

        return $result;
    }

    /**
     * Search using Tomba API.
     * https://developer.tomba.io/#domain-search
     *
     *  This method sends a request to the Tomba API to search for email addresses
     *  associated with the specified domain. It handles various response statuses
     *  and categorizes the found emails based on their verification status and other criteria.
     *
     * @return array{
     *     emails: array{
     *       email: string,
     *       first_name: string,
     *       last_name: string,
     *       full_name: string,
     *       position: string,
     *       linkedin: string
     *     },
     *     confidence: string,
     *     status_code: int,
     *     status: string,
     *     source: string
     * }
     * @throws GuzzleException
     */
    protected function searchTomba()
    {
        $this->logInfo("searchTomba");

        $result = [
            "emails" => [],
            "confidence" => null,
            "status_code" => null,
            "status" => "error",
            "source" => "tomba"
        ];

        $http = new HttpClient(['base_uri' => 'https://api.tomba.io/v1/']);
        $status = null;
        $response = null;
        $query = [
            'domain' => $this->domain->name
        ];

//        $this->logInfo("tombakey: ".config('app.emailFinders.tomba.apiKey'));
//        $this->logInfo("tombasecret: ".config('app.emailFinders.tomba.apiSecret'));

        try {
            $response = $http->get('domain-search', [
                'headers' => [
                    'X-Tomba-Key' => config('app.emailFinders.tomba.apiKey'),
                    'X-Tomba-Secret' => config('app.emailFinders.tomba.apiSecret'),
                ],
                'query' => $query
            ]);

            $status = $response->getStatusCode();

        } catch (ClientException $e) {
            $status = $e->getResponse()->getStatusCode();
            $response = null;

            $this->logError("failed with status {$status}");
            $this->logError(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));

            $errorMessage = $e->getMessage();

        } catch (RequestException $e) {
            $status = $e->getResponse()->getStatusCode();
            $response = null;

            $this->logError("failed with status {$status}");
            $this->logError(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));

            $errorMessage = $e->getMessage();
            if (Str::contains($errorMessage, 'authentication_failed')) {
                $status = 401;
            }
        }

        $this->logInfo("tomba status: ". $status . " - ". $this->domain->name);

        $result['status_code'] = $status;

        if ($status == 400 or $status == 429) {
            $notificationMessage = 'Billing error.';
            $this->externalServiceHandler->notifyServiceError('tomba', 'DataSearch: Tomba', 400, $notificationMessage);
            $this->externalServiceHandler->setServiceError('tomba', 400, 'critical', $notificationMessage);

            $result['status'] = 'apikey_error';
            ApiUsageLog::create([
                'api' => 'tomba',
                'uri' => 'domain-search',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;

        } elseif ($status == 401) {
            $notificationMessage = 'API Key Error. Please validate API Keys.';
            $this->externalServiceHandler->notifyServiceError('tomba', 'DataSearch: Tomba', 401, $notificationMessage);
            $this->externalServiceHandler->setServiceError('tomba', 401, 'critical', $notificationMessage);

            $result['status'] = 'apikey_error';
            ApiUsageLog::create([
                'api' => 'tomba',
                'uri' => 'domain-search',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;

        } elseif (in_array($status, [403, 404, 410, 418])) {
            // candidate for !has_contact
            $result['status'] = 'not_found';
            ApiUsageLog::create([
                'api' => 'tomba',
                'uri' => 'domain-search',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;

        } elseif (in_array($status, [402, 500, 503, 514])) {
            // ignore retry so it will be included in the next searches hoping results will be available
            $result['status'] = 'retry';
            ApiUsageLog::create([
                'api' => 'tomba',
                'uri' => 'domain-search',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;

        } elseif ($status == 200 && $response) {
            $response->getBody()->rewind();
            $responseData = collect(json_decode($response->getBody()->getContents(), true));
            $emailAddresses = data_get($responseData, 'data.emails', []);

            if (empty($emailAddresses)) {
                $result['status'] = 'not_found';
                ApiUsageLog::create([
                    'api' => 'tomba',
                    'uri' => 'domain-search',
                    'status' => $status,
                    'result' => $result['status'],
                ]);

                return $result;
            }

            $ceoEmails = [];
            $verifiedEmails = [];
            $namedEmails = [];
            $unVerifiedEmails = [];

            foreach ($emailAddresses as $email) {
                $hasName = !empty($email['first_name']) || !empty($email['last_name']) || !empty($email['full_name']);
                $hasOtherData = !empty($email['linkedin']) || !empty($email['position']);
                $isFounder = in_array($email['position'], ['ceo', 'founder', 'president', 'owner']);

                $contactData = array(
                    'email' => $email['email'],
                    'first_name' => $email['first_name'],
                    'last_name' => $email['last_name'],
                    'full_name' => $email['full_name'],
                    'position' => $email['position'],
                    'linkedin' => $email['linkedin'],
                    'phone' => $email['phone_number'],
                    'enrichment_source' => $hasName ? 'tomba' : null,
                    'external_email_confidence' => $email['score'],
                );

//                if ($email['score'] > 50 || $email['verification']['status'] == 'valid') {

                if ($email['verification']['status'] == 'valid' && !empty($email['verification']['date'])) {
                    // Tomba verified
                    $tombaVerificationDate = Carbon::parse($email['verification']['date']);
                    // if the verification date is older than 3 months then we need to verify again
                    if ($tombaVerificationDate->diffInDays(now()) <= 90) {
                        $verifyResult['valid'] = true;
                        $contactData['verified_at'] = $tombaVerificationDate;
                        $contactData['verification_error'] = null;
                    } else {
                        // Tomba verified but old, need to verify again
                        $verifyResult = app(EmailVerifierService::class)->verifyEmail($contactData['email'], 'tomba');
                        $contactData['verified_at'] = now();
                        $contactData['verification_error'] = $verifyResult['error'] ?? null;
                    }
                } else {
                    // Tomba not verified, need to verify again
                    $verifyResult = app(EmailVerifierService::class)->verifyEmail($contactData['email'], 'tomba');
                    $contactData['verified_at'] = now();
                    $contactData['verification_error'] = $verifyResult['error'] ?? null;
                }

                if ($verifyResult['valid']) {
                    $this->logInfo("Email verified: " . $contactData['email']);
                    if ($hasName && !empty($email['linkedin']) &&  $isFounder) {
                        // Found CEO email with linkedin
                        $ceoEmails[] = $contactData;
                    } elseif ($hasName && $hasOtherData) {
                        // Found email with name and more data
                        $namedEmails[] = $contactData;
                    } else {
                        $verifiedEmails[] = $contactData;
                    }
                } else {
                    $this->logInfo("Email verified failed: " . $contactData['email']);
                    $unVerifiedEmails[] = $contactData;
                }
//                } else {
//                    $this->logInfo("Low score or invalid emails found.");
//                    $unVerifiedEmails[] = $contactData;
////                }
            }

            // use founder's verified emails first, then other verified
            if (!empty($ceoEmails) || !empty($namedEmails) || !empty($verifiedEmails)) {
                $result['status'] = 'verified';
                $result['confidence'] = 'verified';
                ApiUsageLog::create([
                    'api' => 'tomba',
                    'uri' => 'domain-search',
                    'status' => $status,
                    'result' => 'found emails '.$result['status'],
                ]);

            } elseif (!empty($unVerifiedEmails)) {
                $result['status'] = 'not_verified';
                $result['confidence'] = 'not_verified';
                ApiUsageLog::create([
                    'api' => 'tomba',
                    'uri' => 'domain-search',
                    'status' => $status,
                    'result' => 'found emails '.$result['status'],
                ]);
            }

            $result['emails'] = array_merge($ceoEmails, $namedEmails, $verifiedEmails, $unVerifiedEmails);
            return $result;

        } else {
            // Unidentified response
            if ($response) {
                $reason = $response->getReasonPhrase();
                $this->logError("Unidentified response $status. $reason");
            } else {
                $this->logError("Unidentified response $status.");
            }

            $result['status'] = 'unhandled_error';
            ApiUsageLog::create([
                'api' => 'tomba',
                'uri' => 'domain-search',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;
        }
    }

    /**
     * Search emails of domain using Anymailfinder API.
     * https://anymailfinder.com/email-finder-api/docs#email_search_domain
     *
     * This method sends a request to the Anymailfinder API to search for email addresses
     * associated with the specified domain. It handles various response statuses
     * and categorizes the found emails based on their verification status and other criteria.
     *
     * @return array{
     *     emails: array{
     *       email: string,
     *       first_name: string,
     *       last_name: string,
     *       full_name: string,
     *       position: string,
     *       linkedin: string
     *     },
     *     confidence: string,
     *     status_code: int,
     *     status: string,
     *     source: string
     * }
     * @throws GuzzleException
     */
    protected function searchAnymailFinder()
    {
        $this->logInfo("searchAnymailFinder");

        $result = [
            "emails" => [],
            "confidence" => null,
            "status_code" => null,
            "status" => "error",
            "source" => "anymailfinder"
        ];

        $http = new HttpClient(['base_uri' => 'https://api.anymailfinder.com/v5.0/search/']);
        $status = null;
        $response = null;
        $query = [
            'domain' => $this->domain->name,
            'timeout' => 240,
        ];

        try {
            $response = $http->post('company.json', [
                'headers' => [
                    'X-Api-Key' => config('app.anymailfinderApiKey'),
                ],
                'json' => $query
            ]);

            $status = $response->getStatusCode();

        } catch (RequestException $e) {
            $status = $e->getResponse()->getStatusCode();
            $response = null;

            $this->logError("failed with status {$status}");
            $this->logError(json_encode($e->getMessage(), JSON_INVALID_UTF8_IGNORE));
        }

        if ($status == 401) {
            $notificationMessage = 'API Key Error. Please validate API Keys.';
            $this->externalServiceHandler->notifyServiceError('anymail_finder', 'DataSearch: AnymailFinder', 401, $notificationMessage);
            $this->externalServiceHandler->setServiceError('anymail_finder', 401, 'critical', $notificationMessage);

            $result['status'] = 'apikey_error';
            $result['status_code'] = 401;
            ApiUsageLog::create([
                'api' => 'anymailfinder',
                'uri' => 'company.json',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;

        } elseif ($status == 402) {
            $notificationMessage = 'Payment Required Error. Please check billing.';
            $this->externalServiceHandler->notifyServiceError('anymail_finder', 'DataSearch: AnymailFinder', 402, $notificationMessage);
            $this->externalServiceHandler->setServiceError('anymail_finder', $status, 'critical', $notificationMessage);

            $result['status'] = 'billing_error';
            $result['status_code'] = 402;
            ApiUsageLog::create([
                'api' => 'anymailfinder',
                'uri' => 'company.json',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;

        } elseif (in_array($status, [400, 404, 451])) {
            // candidate for !has_contact
            $result['status'] = 'not_found';
            $result['status_code'] = 404;
            ApiUsageLog::create([
                'api' => 'anymailfinder',
                'uri' => 'company.json',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;

        } elseif (in_array($status, [202, 429, 500, 504, 514])) {
            // ignore retry so it will be included in the next searches hoping results will be available
            $result['status'] = 'retry';
            $result['status_code'] = 429;
            ApiUsageLog::create([
                'api' => 'anymailfinder',
                'uri' => 'company.json',
                'status' => $status,
                'result' => $result['status'],
            ]);

            return $result;

        } elseif ($status == 200 && $response) {
            // Success!
            $this->logInfo("Response $status. Email Found!");
            $result['status_code'] = 200;
            $response->getBody()->rewind();
            $responseData = collect(json_decode($response->getBody()->getContents(), true));
            $status = data_get($responseData, 'success');
            $emailAddresses = data_get($responseData, 'results.emails', []);

            if (!$status || !count($emailAddresses)) {
                $result['status'] = 'not_found';
                $result['status_code'] = 404;
                ApiUsageLog::create([
                    'api' => 'anymailfinder',
                    'uri' => 'company.json',
                    'status' => $status,
                    'result' => $result['status'],
                ]);

                return $result;
            }

            $validation = data_get($responseData, 'results.validation');
//            $confidence = $validation == 'valid' ? 'verified' : 'not_verified'; // We verify everything ourselves.
            $confidence = 'not_verified'; // We verify everything ourselves. This way we only say verified if we get at least one verified email.
            $emails = [];
            $this->logInfo('Got '. count($emailAddresses) . ' emails from AnymailFinder with validation: ' . $validation);

            foreach ($emailAddresses as $email) {
                // Run extra verification
//                if ($confidence === 'verified') {
                $verifyResult = app(EmailVerifierService::class)->verifyEmail($email, 'anymailfinder');
                if ($verifyResult['valid']) {
                    $this->logInfo("Email verified: " . $email);
                    $confidence = 'verified';
                    $emails[] = [
                        'email' => $email,
                        'first_name' => null,
                        'last_name' => null,
                        'full_name' => null,
                        'position' => null,
                        'linkedin' => null,
                        'enrichment_source' => null,
                        'verified_at' => now(),
                        'verification_error' => $verifyResult['error'] ?? null,
                        'external_email_confidence' => 75,
                    ];
                } else {
                    $this->logInfo("Email verified failed: " . $email);
                    $emails[] = [
                        'email' => $email,
                        'first_name' => null,
                        'last_name' => null,
                        'full_name' => null,
                        'position' => null,
                        'linkedin' => null,
                        'enrichment_source' => null,
                        'verified_at' => now(),
                        'verification_error' => $verifyResult['error'] ?? null,
                        'external_email_confidence' => $validation == 'valid' ? 60 : 50,
                    ];
                }
//                } else {
//                    $this->logInfo("Confidence not_verified: " . $email);
//                }
            }

            ApiUsageLog::create([
                'api' => 'anymailfinder',
                'uri' => 'company.json',
                'status' => $result['status_code'],
                'result' => 'found emails '.$confidence,
            ]);

            return [
                "emails" => $emails,
                "confidence" => $confidence,
                "status" => $confidence,
                "status_code" => 200,
                "source" => "anymailfinder"
            ];

        } else {
            if ($response) {
                $reason = $response->getReasonPhrase();
                $this->logError("Unidentified response $status. $reason");
            } else {
                $this->logError("Unidentified response $status.");
            }

            $result['status'] = 'unhandled_error';
            $result['status_code'] = $status;

            ApiUsageLog::create([
                'api' => 'anymailfinder',
                'uri' => 'company.json',
                'status' => $result['status_code'],
                'result' => $result['status'],
            ]);

            return $result;
        }
    }

    /**
     * Return an array of active sources that are enabled in the settings.
     *
     * @return array
     */
    protected function getAvailableSources()
    {
        $sources = [];

        $activeServices = ExternalService::whereIn('name', ['anymail_finder', 'tomba'])
            ->where('status', 'active')
            ->orderBy('id', 'desc')
            ->get();

        $activeServiceNames = $activeServices->pluck('name')->toArray();
        Log::info($activeServiceNames);

        foreach ($this->sources as $source) {
            if (in_array($source, $activeServiceNames)) {
                array_push($sources, $source);
            }
        }

        return $sources;
    }

    /**
     * UNUSED
     * Given an array of emails, complete each email profile by suggesting first and last names.
     *
     * @param array $emails The array of emails to complete.
     * @return array The array of completed email profiles.
     */
    protected function completeEmailProfiles($emails)
    {
        $emailContacts = [];
        $emailNamePattern = $this->getEmailNamesPattern($emails);
        // Log::info("emailNamePattern is:");
        // Log::info($emailNamePattern);

        foreach ($emails as $emailContact) {
            $emailParts = explode('@', $emailContact['email']);
            $emailName = $emailParts[0];
            $commonNames = commonCompanyEmailNames();

            // check if email name is generic
            if(in_array(strtolower($emailName), $commonNames)) {
                $this->logInfo("completeEmailProfiles - " . $emailName. " is in commonNames");
                array_push($emailContacts, $emailContact);
                continue;
            }

            $suggestedNames = $this->getSuggestedEmailName($emailContact, $emailNamePattern["pattern"], $emailNamePattern["separator"]);

            if(!empty($suggestedNames)) {
                $emailContact['suggested_names'] = $suggestedNames;
            }

            array_push($emailContacts, $emailContact);
        }

        return $emailContacts;
    }

    /**
     * Based on the given pattern, generate an array of suggested names.
     * The given pattern is based on the email list of the given company.
     * The suggested names are generated by parsing the email name and
     * extracting the first name and last name.
     *
     * The returned array has the following structure:
     * [
     *     'first_name' => string,
     *     'last_name' => string,
     *     'first_selected' => string,
     *     'last_selected' => string,
     *     'first_names' => array,
     *     'last_names' => array
     * ]
     *
     * @param array $emailContact
     * @param string $pattern
     * @param string $separator
     * @return array
     */
    protected function getSuggestedEmailName($emailContact, $pattern, $separator)
    {
        $suggestedNames = array(
            "first_name" => "",
            "last_name" => "",
            "first_selected" => "",
            "last_selected" => "",
            "first_names" => array(),
            "last_names" => array()
        );

        // skip if has firstname
        if(!empty($emailContact['first_name'])) {
            return $suggestedNames;
        }

        // check if emailname is
        $names = $this->getSuggestedNamesFromEmail($emailContact['email'], $pattern, $separator);
        $firstName = "";
        $lastName = "";
        $firstSelected = "";
        $lastSelected = "";

        // if has firstname, use it as selected firstname and
        // add others to toggle input to manually edit suggested name
        if(!empty($names["first_names"])) {
            $firstName = $names["first_names"][0];
            $firstSelected = $names["first_names"][0];

            $names["first_names"][] = "other";
        }

        // if has lastname, use it as selected lastname and
        // add others to toggle input to manually edit suggested name
        if(!empty($names["last_names"])) {
            $lastName = $names["last_names"][0];
            $lastSelected = $names["last_names"][0];

            $names["last_names"][] = "other";
        }

        $suggestedNames = array(
            "first_name" => $firstName,
            "last_name" => $lastName,
            "first_selected" => $firstSelected,
            "last_selected" => $lastSelected,
            "first_names" => $names["first_names"],
            "last_names" => $names["last_names"]
        );

        return $suggestedNames;
    }

    /**
     * Finds the most common pattern of first and last name in emailContacts.
     *
     * Returns an array with two keys: "pattern" and "separator".
     * "pattern" examples: "fname_lname", "fname.lname", "fname", "fnamelname", "f.lname"
     * "separator" can be "dot", "underscore" or "hypen"
     *
     * @param  array  $emailContacts
     * @return array
     */
    protected function getEmailNamesPattern($emailContacts)
    {
        $result = array(
            "pattern" => "",
            "separator" => ""
        );

        $combinations = array(
            "fname_lname" => 0,
            "fname.lname" => 0,
            "fname" => 0,
            "lname" => 0, // :(
            "flname" => 0,
            "fnamel" => 0, // :(
            "fnamelname" => 0,
            "lnamefname" => 0,  // :(
            "lname.fname" => 0,
            "lname_fname" => 0, // :(
            "fname-lname" => 0,
            "f-lname" => 0,
            "fname-l" => 0,
            "f.lname" => 0,
            "fname.l" => 0,
            "f_lname" => 0,
            "fname_l" => 0
        );

        $separators = array(
            "dot" => 0,
            "underscore" => 0,
            "hypen" => 0,
        );

        foreach ($emailContacts as $emailContact) {
            $emailParts = explode('@', $emailContact['email']);
            $emailName = strtolower($emailParts[0]);
            $separator = null;
            $fname = null;
            $lname = null;

            if(Str::contains($emailName, '.')) {
                $separator = ".";
                $separators["dot"] = $separators["dot"] + 1;
            } elseif (Str::contains($emailName, '_')) {
                $separator = "_";
                $separators["underscore"] = $separators["underscore"] + 1;
            } elseif (Str::contains($emailName, '-')) {
                $separator = "-";
                $separators["hypen"] = $separators["hypen"] + 1;
            }

            if($separator) {
                // if has separator, detect fname and lname pattern
                $emailNameParts = explode($separator, $emailName);
                $first = $emailNameParts[0];
                $last = $emailNameParts[1];

                if(strlen($first) == 1) {
                    $combination = "f".$separator."lname";
                    $combinations[$combination] += 1;
                } elseif (strlen($last) == 1) {
                    $combination = "fname".$separator."l";
                    $combinations[$combination] += 1;
                } else {
                    $combination = "fname".$separator."lname";
                    $combinations[$combination] += 1;
                }
            } else {
                // Log::info($emailName);
                $consonants = consonants();

                // else detect combination of
                if (strlen($emailName) > 12) {
                    // if long name, fnamelname
                    $combinations["fnamelname"] += 1;
                } elseif (strlen($emailName) >= 2 && in_array($emailName[0], $consonants) && in_array($emailName[1], $consonants)) {
                    // if 2 consonants, flastname
                    $combinations["flname"] += 1;
                } else {
                    $combinations["fname"] += 1;
                }
            }
        }

        arsort($combinations);
        $topPattern = key($combinations);

        arsort($separators);
        $topSeparator = key($separators);

        if(!empty($combinations[$topPattern])) {
            $result["pattern"] = $topPattern;
        }

        if(!empty($separators[$topSeparator])) {
            $result["separator"] =  $topSeparator;
        }

        return $result;
    }

    /**
     * Generate suggested names based on the given email address and pattern
     *
     * @param  string $emailAddress The email address to generate names from
     * @param  string $pattern       The pattern to use for generating names
     * @param  string $separator     The separator to use for generating names
     * @return array                An array with "first_names" and "last_names"
     *                              containing the suggested names
     */
    protected function getSuggestedNamesFromEmail($emailAddress, $pattern, $separator)
    {
        $suggestedNames = array(
            "first_names" => [],
            "last_names" => []
        );

        $emailParts = explode('@', $emailAddress);
        $emailName = strtolower($emailParts[0]);
        $emailNameParts = [];

        switch ($pattern) {
            case 'flname':
                $suggestedNames["first_names"][] = ucfirst($emailName[0]);
                $suggestedNames["last_names"][] = ucfirst(substr($emailName, 1));
                break;
            case 'fname_lname':
            case 'fname.lname':
            case 'fname-lname':
            case 'f-lname':
            case 'fname-l':
            case 'f.lname':
            case 'fname.l':
            case 'f_lname':
            case 'fname_l':
                if(!empty($separator)) {
                    $emailNameParts = explode($separator, $emailName);

                    if(!empty($emailNameParts[0])) {
                        $suggestedNames["first_names"][] =  ucfirst($emailNameParts[0]);
                    }

                    if(!empty($emailNameParts[1])) {
                        $suggestedNames["last_names"][] = ucfirst($emailNameParts[1]);
                    }
                } else {
                    $suggestedNames["first_names"][] = ucfirst($emailName);
                }
                break;

            default:
                // treat as fname
                $suggestedNames["first_names"][] = ucfirst($emailName);
                break;
        }

        return $suggestedNames;
    }

    protected function logInfo($msg)
    {
        $message = "EmailsFinderService Domain-{$this->domain->name} ";
        if (!empty($this->searchId)) {
            $message .= "AgencySearch-$this->searchId ";
        }
        Log::info("$message: $msg");
    }

    protected function logError($msg)
    {
        $message = "EmailsFinderService Domain-{$this->domain->name} ";
        if (!empty($this->searchId)) {
            $message .= "AgencySearch-$this->searchId ";
        }
        Log::error("$message: $msg");
    }
}
