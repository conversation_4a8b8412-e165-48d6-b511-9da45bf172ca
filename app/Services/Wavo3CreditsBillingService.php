<?php

namespace App\Services;

use App\Agency;
use App\Exceptions\InsufficientAvailableCreditsException;
use App\Exceptions\InsufficientCreditsException;
use App\Exceptions\InsufficientLockedCreditsException;
use Illuminate\Support\Facades\DB;

class Wavo3CreditsBillingService
{
    public function __construct(protected Agency $agency)
    {
        // Constructor
    }

    // Returns the total remaining credits
    public function getRemainingCredits()
    {
        $this->agency = $this->agency->fresh();
        return $this->agency->credits;
    }

    // Returns the currently available credits (that are not locked)
    public function getAvailableCredits()
    {
        $this->agency = $this->agency->fresh();
        return $this->agency->credits - $this->agency->locked_credits;
    }

    public function incrementCredits($amount, $reason)
    {
        if (empty($amount) || ($amount == 0)) {
            return;
        }

        DB::connection('mysql::write')->transaction(function () use ($amount, $reason) {
            $agency = DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->lockForUpdate()
                ->first();

            DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->update(['credits' => $agency->credits + $amount]);

            $this->agency->creditLogs()->create([
                'amount' => $amount,
                'reason' => $reason,
                'type' => 'increment',
            ]);
        });
    }

    public function decrementCredits($amount, $reason)
    {
        if (empty($amount) || ($amount == 0)) {
            return;
        }

        DB::connection('mysql::write')->transaction(function () use ($amount, $reason) {
            $agency = DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->lockForUpdate()
                ->first();

            if ($agency->credits < $amount) {
                throw new InsufficientCreditsException();
            }

            DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->update(['credits' => $agency->credits - $amount]);

            $this->agency->creditLogs()->create([
                'amount' => $amount,
                'reason' => $reason,
                'type' => 'decrement',
            ]);
        });
    }

    // Lock credits to spend (i.e. to search for emails)
    public function lockCredits($amount, $reason)
    {
        if (empty($amount) || ($amount == 0)) {
            return;
        }

        DB::connection('mysql::write')->transaction(function () use ($amount, $reason) {
            $agency = DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->lockForUpdate()
                ->first();

            if (($agency->credits - $agency->locked_credits) < $amount) {
                if ($agency->credits < $amount) {
                    // No credits available. Throw exception
                    throw new InsufficientCreditsException();
                } else {
                    // Credits are available but locked. Throw exception
                    throw new InsufficientAvailableCreditsException();
                }
            }

            DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->update(['locked_credits' => $agency->locked_credits + $amount]);

            $this->agency->creditLogs()->create([
                'amount' => $amount,
                'reason' => $reason,
                'type' => 'lock',
            ]);
        });
    }

    // Release credits if not used (i.e. email search unsuccessful)
    public function releaseLockedCredits($amount, $reason)
    {
        if (empty($amount) || ($amount == 0)) {
            return;
        }

        DB::connection('mysql::write')->transaction(function () use ($amount, $reason) {
            $agency = DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->lockForUpdate()
                ->first();

            if ($agency->locked_credits < $amount) {
                throw new InsufficientLockedCreditsException();
            }

            DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->update(['locked_credits' => $agency->locked_credits - $amount]);

            $this->agency->creditLogs()->create([
                'amount' => $amount,
                'reason' => $reason,
                'type' => 'release',
            ]);
        });
    }

    // Finalize credit usage (i.e. email search was successful)
    public function finalizeLockedCredits($amount, $reason)
    {
        if (empty($amount) || ($amount == 0)) {
            return;
        }

        DB::connection('mysql::write')->transaction(function () use ($amount, $reason) {
            $agency = DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->lockForUpdate()
                ->first();

            if ($agency->locked_credits < $amount) {
                throw new InsufficientLockedCreditsException();
            }

            if ($agency->credits < $amount) {
                throw new InsufficientCreditsException();
            }

            DB::connection('mysql::write')->table('agencies')
                ->where('id', $this->agency->id)
                ->update([
                    'locked_credits' => $agency->locked_credits - $amount,
                    'credits' => $agency->credits - $amount,
                ]);

            $this->agency->creditLogs()->create([
                'amount' => $amount,
                'reason' => $reason,
                'type' => 'usage',
            ]);
        });
    }
}
