<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;

class AgencyController extends Controller
{
    /**
     * Get the current user's agency with subscription data.
     *
     * @return mixed
     */
    public function current()
    {
        if (Auth::user()->can('agency-admin')) {
            return Auth::user()->agency->load('subscriptions')->shouldHaveSelfVisibility();
        }

        return Auth::user()->agency;
    }
}
