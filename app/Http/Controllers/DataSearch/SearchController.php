<?php

namespace App\Http\Controllers\DataSearch;

use App\Agency;
use App\Campaign;
use App\Contact;
use App\CreditLog;
use App\Http\Controllers\Controller;
use App\Jobs\StoreLeadsEmailFinder;
use App\Jobs\StoreLeadsFindContacts;
use App\Jobs\UpdateDuplicateProspects;
use App\Models\StoreLeads\DomainData;
use App\Models\StoreLeads\Platform;
use App\Models\StoreLeads\Plan;
use App\Models\StoreLeads\Category;
use App\Models\StoreLeads\Country;
use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\App;
use App\Models\StoreLeads\ContactType;
use App\Models\StoreLeads\Currency;
use App\Models\StoreLeads\Feature;
use App\Models\StoreLeads\Language;
use App\Models\StoreLeads\Month;
use App\Models\StoreLeads\PhoneCountryCode;
use App\Models\StoreLeads\SalesChannel;
use App\Models\StoreLeads\SearchedDomain;
use App\Models\StoreLeads\ShippingCarrier;
use App\Models\StoreLeads\ShippingCountry;
use App\Models\StoreLeads\Tag;
use App\Models\StoreLeads\Technology;
use App\Models\StoreLeads\Theme;
use App\Models\StoreLeads\ThemeVendor;
use App\Models\StoreLeads\TopLevelDomain;
use App\Models\StoreLeads\AgencyDomain;
use App\Models\StoreLeads\AgencyEmail;
use App\Models\StoreLeads\Search;
use App\Models\StoreLeads\AgencyList;
use App\Models\StoreLeads\AgencyFilter;
use App\Prospect;
use App\Services\JourneyService;
use App\Support\DomainFilterNormalizer;
use Carbon\Carbon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Services\Wavo3CreditsBillingService;
use App\Services\EmailsFinderService;
use App\Services\DomainNormalizationService;
use App\Services\EmailVerifierService;
use App\Services\DomainContactsFinderService;
use App\Services\DomainContactsQualityService;
use Illuminate\Pagination\LengthAwarePaginator;

class SearchController extends Controller
{
    private const TOTAL_DOMAINS = 13032777;
    private const SEARCHED_MONTHS_AGO = 4;
    private const VERIFIED_MONTHS_AGO = 6;
    private $displayClientSelect = false;

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (
                (Auth::user()->can('agency-admin') &&
                    Auth::user()->agency->isAgencyDashboard()) ||
                (Auth::user()->isAdmin() || Auth::user()->isSupport())
            ) {
                $this->displayClientSelect = true;
            }

            $this->isSupport = Auth::user()->isAdmin() || Auth::user()->isSupport();

            return $next($request);
        });
    }

    /*
     * The main search page v2 (apollo style search)
     */
    public function index()
    {
        $teams = Auth::user()->getAccessibleTeams();

        $campaigns = Campaign::whereIn('team_id', $teams->pluck('id')->toArray())
            ->orderBy('created_at', 'desc')
            ->get();

        return view('data-search.companies.index', [
            'totalDomains' => self::TOTAL_DOMAINS,
            'isSupport' => $this->isSupport,
            'teams' => $teams,
            'campaigns' => $campaigns,
            'displayClientSelect' => $this->displayClientSelect
        ]);
    }

    /*
     * The main search page v1 (/search/v1)
     */
    public function indexSearch()
    {
        $searches = Search::where('agency_id', Auth::user()->agency_id)
            ->orderByDesc('id')
            ->get();

        return view('data-search.search.index-search', [
            'searches' => $searches,
            'totalDomains' => self::TOTAL_DOMAINS,
            'isSupport' => $this->isSupport,
            'teams' => Auth::user()->agency->teams()->select('id','name')->get()
        ]);
    }

    /*
     * Returns the list of filters and their available options
     */
    public function filters()
    {
        $categories = Category::whereNull('parent_id')
            ->with(['children'=>function($c){
                $c->with(['children'=>function($c){
                    $c->orderBy('domain_count', 'desc');
                }])->orderBy('domain_count', 'desc');
            }])
            ->orderBy('domain_count', 'desc')
            ->get();

        $specificCodes = ['unknown', 'US', 'CA'];
        $firstCountries = Country::whereIn('code', $specificCodes)
            ->selectRaw('id, CONCAT(name, " (", code, ")") as name, code')
            ->orderByRaw("FIELD(code, '" . implode("','", $specificCodes) . "')")
            ->get();
        $remainingCountries = Country::whereNotIn('code', $specificCodes)
            ->orderByDesc('domain_count')
            ->selectRaw('id, CONCAT(name, " (", code, ")") as name, code')
            ->get();
        $countries = $firstCountries->merge($remainingCountries);

        $socialContactTypes = [
            'email' => 'Email',
            'facebook' => 'Facebook',
            'facebookgroup' => 'Facebook Group',
            'instagram' => 'Instagram',
            'linkedin' => 'LinkedIn',
            'phone' => 'Phone',
            'pinterest' => 'Pinterest',
            'snapchat' => 'Snapchat',
            'tiktok' => 'TikTok',
            'twitter' => 'Twitter',
            'whatsapp' => 'WhatsApp',
            'yelp' => 'Yelp',
            'youtube' => 'YouTube',
        ];
        $contactTypes = ContactType::get()->each(function ($contactType) use ($socialContactTypes) {
            $contactType->name = $socialContactTypes[$contactType->name] ?? $contactType->name;
        });

        $apps = App::orderByDesc('domain_count')->limit(200)->get();
        $currencies = Currency::orderByDesc('domain_count')->get();
        $features = Feature::orderByDesc('domain_count')->get();
//        $languages = Language::orderByDesc('domain_count')->get()->each(function ($language) {
//            if (!empty($language->name)) {
//                $language->name = $language->name . ' (' . $language->code . ')';
//            } else {
//                $language->name = $language->code;
//            }
//        });
        $months = Month::orderByDesc('name')->get();
        $phoneCountryCodes = PhoneCountryCode::get();
        $salesChannels = SalesChannel::orderByDesc('domain_count')->get();
        $shippingCarriers = ShippingCarrier::orderByDesc('domain_count')->get();
//        $shippingCountries = ShippingCountry::orderByDesc('domain_count')->get();
        $tags = Tag::orderByDesc('domain_count')->get();
        $technologies = Technology::orderByDesc('domain_count')->limit(200)->get();
//        $themes = Theme::get();
//        $themeVendors = ThemeVendor::get();
        $topLevelDomains = TopLevelDomain::get();
        $estimatedSales = Domain::ESTIMATED_SALES;
        $productCounts = Domain::PRODUCT_COUNTS;
        $platforms = Platform::orderByDesc('domain_count')
            ->selectRaw('id, nice_name as name')
            ->with('plans')
            ->get();

        return [
            "status" => "success",
            "categories" => $categories,
            "countries" => $countries,
            "apps" => $apps,
            "contact_types" => $contactTypes,
            "currencies" => $currencies,
            "features" => $features,
//            "domain_languages" => $languages,
            "months" => $months,
            "phone_country_codes" => $phoneCountryCodes,
            "sales_channels" => $salesChannels,
            "shipping_carriers" => $shippingCarriers,
//            "shipping_countries" => $shippingCountries,
            "tags" => $tags,
            "technologies" => $technologies,
            // "themes" => $themes,
            // "theme_vendors" => $themeVendors,
            "top_level_domains" => $topLevelDomains,
            "estimated_sales" => $estimatedSales,
            "product_counts" => $productCounts,
            "platforms" => $platforms
        ];
    }

    /*
     * Returns the list of all searchable domains for the agency
     * Excludes domains that the agency has already saved
     */
    public function searchCursorDomains(Request $request, $hidden = 0)
    {
        $paginateCount = 50;

        $viewType = !empty($request->view) ? $request->view : null; // total, new, saved
        $categoryIds = !empty($request->categories) ? $request->categories : [];
        $countryIds = !empty($request->countries) ? $request->countries : [];
        $saleIds = !empty($request->estimated_sales) ? $request->estimated_sales : [];
        $productCountIds = !empty($request->product_counts) ? $request->product_counts : [];
        $platformIds = !empty($request->platforms) ? $request->platforms : [];
        $appIds = !empty($request->apps) ? $request->apps : [];
        $contactTypeIds = !empty($request->contact_types) ? $request->contact_types : [];
        $currencyIds = !empty($request->currencies) ? $request->currencies : [];
        $featureIds = !empty($request->features) ? $request->features : [];
        $languageIds = !empty($request->domain_languages) ? $request->domain_languages : [];
        $monthIds = !empty($request->months) ? $request->months : [];
        $salesChannelIds = !empty($request->sales_channels) ? $request->sales_channels : [];
        $shippingCarrierIds = !empty($request->shipping_carriers) ? $request->shipping_carriers : [];
        $shippingCountryIds = !empty($request->shipping_countries) ? $request->shipping_countries : [];
        $tagIds = !empty($request->tags) ? $request->tags : [];
        $technologyIds = !empty($request->technologies) ? $request->technologies : [];
        $planIds = !empty($request->plans) ? $request->plans : [];

        // exclude platforms if plans is available because plan is kindof sub-platform filter
        if(!empty($planIds)) {
            $platformIds = [];
        }

        $domains = Domain::select(
                'id', 'name', 'employee_count_id', 'estimated_sales_id',
                'product_count_id', 'region_id', 'country_id', 'platform_id',
                'plan_id', 'merchant_name'
            )
            ->ofApps($appIds)
            ->ofCategories($categoryIds)
            ->ofCountries($countryIds)
            ->ofContactTypes($contactTypeIds)
            ->ofCurrencies($currencyIds)
            ->ofFeatures($featureIds)
            ->ofLanguages($languageIds)
            ->ofMonths($monthIds)
            ->ofProductCounts($productCountIds)
            ->ofPlatforms($platformIds)
            ->ofPlans($planIds)
            ->ofSales($saleIds)
            ->ofSalesChannels($salesChannelIds)
            ->ofShippingCarriers($shippingCarrierIds)
            ->ofShippingCountries($shippingCountryIds)
            ->ofTags($tagIds)
            ->ofTechnologies($technologyIds)

            ->with([
                'categories:id,name',
                'country:id,name,code',
                'platform:id,nice_name',
                'domainData:id,data',
                'plan:id,name',
                // 'agencyDomain.agencyLists' => function($q) {
                //     $q->where('agency_id', Auth::user()->agency_id);
                // },
                // 'agencyDomains' => function($q) {
                //     $q->where('agency_id', Auth::user()->agency_id)
                //         ->with(['agencyLists']);
                // },
                'agencyDomains' => function($q) {
                    $q->where('agency_id', Auth::user()->agency_id)
                        ->with(['email', 'agencyLists' => function($q) {
                            $q->where('user_id', Auth::user()->id);
                        }])
                        ->withCount(['emails']);
                },
                // 'searchedDomain'
            ])
            ->cacheFor(Domain::CACHE_FOR);

        if (empty($request->cursor)) {
            $domains = $domains->where('sl_domains.id','>', 0);
        }

        $domains = $domains->orderBy('sl_domains.id')->cursorPaginate($paginateCount);

        // get data.icon only and remove domainData on the response
        $domains->getCollection()->transform(function ($domain, $key) {
            $data =  $domain->domainData->data ?? null;
            $img = $data['og_image'] ?? null;
            $icon = $data['icon'] ?? null;
            $merchantName = $domain->merchant_name ?? null;
            $socialContacts = null;

            if(!empty($data['contact_info'])) {
                foreach ($data['contact_info'] as $contact) {
                    $socialContacts[$contact['type']] = $contact['value'];
                }
            }

            if(empty($merchantName)) {
                $merchantName = $data['merchant_name'] ?? null;
            }

            if(Str::startsWith($merchantName, 'www.')) {
                $merchantName = ltrim($merchantName, 'www.');
            }

            $domain->icon = $icon ?? $img;
            $domain->merchant_name = $merchantName;
            $domain->contacts = $socialContacts;
            unset($domain->domainData);

            // display censored email (fake) for consistency
            // $domainParts = explode('.', urlTrim($domain->name));
            // $mainDomain = implode('.', array_slice($domainParts, -2));
            $mainDomain = getMainDomain(urlTrim($domain->name));
            $emailName = "";
            $emailCharCount = 3;
            $nameLength = strlen($merchantName);

            // dynamic length of asterisk based on merchant name length
            if ($nameLength >= 20) {
                $emailCharCount = 5;
            } else if ($nameLength < 20 && $nameLength >= 10) {
                $emailCharCount = intval(ceil($nameLength / 3));
            } else if ($nameLength < 10 && $nameLength >= 4) {
                $emailCharCount = intval(ceil($nameLength / 2));
            } else {
                $emailCharCount = $nameLength * 2;
            }

            for ($i=0; $i < $emailCharCount; $i++) {
                $emailName = $emailName."*";
            }

            $domain->contact_email = $emailName."@".$mainDomain;

            // check if domain is already saved by this agency
            $listIds = [];
            // $agencyDomainId = null;
            $agencyDomain = null;

            if($domain->agencyDomains->count()) {
                $ad = $domain->agencyDomains->first();
                // $agencyDomainId = $ad->id;
                // $domain->has_agency_domain = true;

                $agencyDomain = array(
                    'id' => $ad->id,
                    'status' => $ad->email_search_status,
                    'list_ids' => array(),
                    'contacts_count' => $ad->emails_count
                );

                // transform agency_lists
                if($ad->agencyLists->count()) {
                    $agencyDomain['list_ids'] = $ad->agencyLists->pluck('id')->toArray();
                }

                // display contact's email
                if(!empty($ad->email->email)) {
                    $domain->contact_email = $ad->email->email;
                }
            } else {
                // $domain->has_agency_domain = false;
            }

            unset($domain->agencyDomains);

            // $domain->agency_domain_id = $agencyDomainId;
            $domain->agency_domain = $agencyDomain;
            $domain->list_ids = $listIds;


            // for vue state
            $domain->is_fetching = false;
            $domain->is_saving = false;

            return $domain;
        });

        // If no domains found, then do no more filtering
        if ($domains->isEmpty()) {

            return [
                "status" => "success",
                "domains" => $domains,
                'hidden' => 0,
            ];
        }

        // Check if we need to hide results (already on agency or failed search)
//        $domainIds = $domains->pluck('id');
//        $agencyId = Auth::user()->agency_id;
//        $agencyDomainIds = AgencyDomain::where('agency_id', $agencyId)
//            ->whereIn('domain_id', $domainIds)
//            ->pluck('domain_id');
//        $searchedDomainIds = SearchedDomain::whereIn('id', $domainIds)
//            ->where(function($q) {
//                $q->where('has_failed_search', '>', 3)
//                    ->orWhere(function($query) {
//                        $query->where('has_failed_search', 1)
//                            ->where('last_searched_at', '>=', now()->subDay())
//                            ->orWhere(function($q) {
//                                $q->where('has_failed_search', 2)
//                                    ->where('last_searched_at', '>=', now()->subMonth());
//                            })
//                            ->orWhere(function($q) {
//                                $q->where('has_failed_search', 3)
//                                    ->where('last_searched_at', '>=', now()->subMonths(2));
//                            });
//                    });
//            })
//            ->where('has_contact', 0)
//            ->pluck('id');
//        $hiddenIds = $searchedDomainIds;

        // If no need to hide any results, return the domains
//        if ($hiddenIds->isEmpty()) {
//
//            return response()->json([
//                "status" => "success",
//                "domains" => $domains,
//                'hidden' => $hidden,
//            ], 200);
//        }

        // If all results are hidden - recursive call to get next page if available
//        if ($hiddenIds->count() == $paginateCount) {
//
//            $hidden += $hiddenIds->count();
//
//            if ($domains->nextPageUrl()) {
//                $nextCursor = Str::after($domains->nextPageUrl(), 'cursor=');
//                $request->merge(['cursor' => $nextCursor]);
//
//                return $this->searchCursorDomains($request, $hidden);
//            } else {
//
//                return response()->json([
//                    "status" => "success",
//                    "domains" => $domains,
//                    'hidden' => $hidden,
//                ], 200);
//            }
//        }

        // Hide results that are already saved and return remaining domains
//        $hidden += $hiddenIds->count();

        // get saved agencyDomain to hide based on viewType
        $domainIds = $domains->pluck('id');
        $agencyDomainIds = [];
        $hidden = 0;

        if($viewType == 'total') {
            $agencyDomainIds = AgencyDomain::whereIn('domain_id', $domainIds)
                ->where('agency_id', Auth::user()->agency_id)
                ->where('email_search_status', 'failed')
                ->pluck('domain_id')
                ->toArray();
        } elseif ($viewType == 'new') {
            $agencyDomainIds = AgencyDomain::whereIn('domain_id', $domainIds)
                ->where('agency_id', Auth::user()->agency_id)
                ->pluck('domain_id')
                ->toArray();
        }

        $results = json_decode($domains->toJson());
        $results->data = collect($results->data)->filter(function ($domain) use ($agencyDomainIds, &$hidden) {
            if(in_array($domain->id, $agencyDomainIds)) {
                $hidden++;
                return false;
            }

            return true;
        })->flatten()->all();

        return response()->json([
            "status" => "success",
            "domains" => $results,
            'hidden' => $hidden,
        ], 200);
    }

    /**
     * Returns the total count of domains based on the filters
     */
    public function searchTotalDomains(Request $request)
    {
        if (!$request->filtered) {
            $total = self::TOTAL_DOMAINS;
        } elseif (empty($request->cursor)) {
            // Test Query Cache
            $filters = DomainFilterNormalizer::normalize($request->all());
            $agencyId = null; // Don't exclude agency domains for totals. We will subtract them in the frontend

            $total = Domain::filter($filters, $filters['viewType'] ?? null, $agencyId)
                ->cacheFor(Domain::CACHE_FOR)
                ->count();

            // count failed AgencyDomain based on the criteria
//            $failedCount = AgencyDomain::where('agency_id', Auth::user()->agency_id)
//                ->ofDomainCategories($categoryIds)
//                ->ofDomainCountries($countryIds)
//                ->ofDomainSales($saleIds)
//                ->ofDomainProducts($productCountIds)
//                ->ofDomainApps($appIds)
//                ->ofDomainContactTypes($contactTypeIds)
//                ->ofDomainCurrencies($currencyIds)
//                ->ofDomainFeatures($featureIds)
//                ->ofDomainLanguages($languageIds)
//                ->ofDomainMonths($monthIds)
//                ->ofDomainPlatforms($platformIds)
//                ->ofDomainPlans($planIds)
//                ->ofDomainSalesChannels($salesChannelIds)
//                ->ofDomainShippingCarriers($shippingCarrierIds)
//                ->ofDomainShippingCountries($shippingCountryIds)
//                ->ofDomainTags($tagIds)
//                ->ofDomainTechnologies($technologyIds)
//                ->ofDomainKeywords($keywords)
//                ->where('email_search_status', 'failed')
//                ->count();

//            $total = $count - $failedCount;
        } else {
            $total = null;
        }

        return [
            "status" => "success",
            "total" => $total
        ];
    }

    /**
     * Returns domain data
     * include the agency domain and emails if saved already
     */
    public function showDomainData(DomainData $domainData) {
        $domain = Domain::find($domainData->id);

        $agencyDomain = AgencyDomain::where('domain_id', $domain->id)
            ->where('agency_id', Auth::user()->agency_id)
            ->where('email_search_status', 'success')
            ->select('id', 'domain_id', 'agency_id', 'email_search_status', 'current_email_id')
            ->with(['emails' => function($e) {
                $e->select(
                    'id','domain_id','agency_id','agency_domain_id','primary',
                    'email','primary','first_name','last_name', 'position', 'contact_id'
                )
                ->with(['contact:id,email_activity_confidence,external_email_confidence,verified_at,verification_error,source,seniority_id']);
            }])
            ->first();

        return response()->json([
            'merchant-name' => $domain->merchant_name,
            'domain-data' => $domainData->data,
            'categories' => $domain->categories,
            'country' => $domain->country,
            'agency-domain' => $agencyDomain
        ]);
    }

    /*
     * Save the search including the filters
     * -- version2 where user save search and then dispatch a job until we find the required total of contacts
     */
    public function saveSearch(Request $request)
    {
        $agencyId = Auth::user()->agency_id;
        $userId = Auth::user()->id;

        $this->validate($request, [
            'number_of_contacts' => 'required|integer|min:1',
            'team_id' => [
                function ($attribute, $value, $fail) {
                    if ((Auth::user()->isSupport() || Auth::user()->isStaff() || Auth::user()->isAdmin()) && empty($value)) {
                        $fail('You must select a Client.');
                    }
                },
            ],
        ]);

        // require a search name if number of contacts is 1k, or select_all_type == all
        if($request->select_all_type == "all" || empty($request->domain_ids)) {
            $this->validate($request, [
                'search_name'  => 'required|string|max:255',
            ]);

            // search name should be unique
            $existingSearchName = Search::where('name', $request->search_name)
                ->where('agency_id', $agencyId)
                ->first();

            if(!empty($existingSearchName)) {
                $errName = 'Search name already exists';
                return response()->json([
                    'message' => $errName,
                    'errors' => [
                        'search_name' => [ $errName ]
                    ]
                ], 422);
            }

            $searchName = $request->search_name;
            $searchDomainIds = [];
        } else {
            $searchName = 'Save All: '.now();
            $searchDomainIds = $request->domain_ids;
        }

        // validate filter preset name
        if(!empty($request->filter_as_template)) {
            $this->validate($request, [
                'filter_name'  => 'required|string|max:255',
            ]);

            // filter name should be unique
            $existingFilterName = AgencyFilter::where('name', $request->filter_name)
                ->where('user_id', $userId)
                ->where('agency_id', $agencyId)
                ->first();

            if(!empty($existingFilterName)) {
                $errName = 'Filter name already exists';
                return response()->json([
                    'message' => $errName,
                    'errors' => [
                        'filter_name' => [ $errName ]
                    ]
                ], 422);
            }
        }

        // check credits
        $agency = Agency::find($agencyId);
        $creditService = new Wavo3CreditsBillingService($agency);
        $lockError = false;
        $credits = 0;

        try {
            $credits = $request->number_of_contacts;
            $creditService->lockCredits($credits, $searchName);
            // $lockedCredits = true;
        } catch (\Throwable $e) {
            $lockError = $e->getMessage();
        }

        if(!empty($lockError)) {
            return response()->json([
                'message' => $lockError,
                'errors' => [
                    'credits' => [
                        $lockError
                    ]
                ]
            ], 422);
        }

        // avoid concurrent search
        $hasInprogressSearch = Search::whereIn('status', ['INPROGRESS', 'PAUSED'])
            ->where('agency_id', $agencyId)
            ->exists();

        $searchStatus = $hasInprogressSearch ? 'PAUSED' : 'INPROGRESS';
        $enableEnrichmentApis = $request->enable_enrichment_apis === false ? false : true;

        $teamId = $request->team_id ?? null;
        if (empty($teamId)) {
            $teamId = Auth::user()->teams()->first()->id;
        }

        // need to validate list ids from DB
        $listIds = [];
        $lists = collect();

        if(!empty($request->list_ids)) {
            $listIds = $request->list_ids;

            $lists = AgencyList::whereIn('id', $listIds)
                ->where('agency_id', $agencyId)
                ->select('id', 'name', 'agency_id')
                ->get();
        }

        $search = Search::create([
            'agency_id' => $agencyId,
            'name' => $searchName,
            'total_contacts' => $request->number_of_contacts,
            'search_filters' => $request->filters,
            'status' => $searchStatus,
            'locked_credits' => $credits,
            'enable_enrichment_apis' => $enableEnrichmentApis,
            'suppression_team_id' => $teamId,
            'domain_ids' => $searchDomainIds,
            'list_ids' => $listIds,
            'search_enrichment_phase' => 'enriched',
        ]);

        if($searchStatus == 'INPROGRESS') {
            StoreLeadsFindContacts::dispatch($search)->onQueue('emailfinder');
        }

        // connect saved domain_ids to lists here because they'll
        // never be picked up and tag in "StoreLeadsFindContacts"
        if(!empty($searchDomainIds) && $lists->count()) {
            $agencyDomains = AgencyDomain::whereIn('domain_id', $searchDomainIds)
                ->where('agency_id', $agencyId)
                ->where('email_search_status', 'success')
                ->get();

            $agencyDomainIds = $agencyDomains->pluck('id');

            foreach ($lists as $list) {
                $list->agencyDomains()->syncWithoutDetaching($agencyDomainIds);
            }
        }

        // save filter preset
        if(!empty($request->filter_as_template)) {
            $agencyFilter = AgencyFilter::create([
                'name' => $request->filter_name,
                'user_id' => $userId,
                'agency_id' => $agencyId,
                'filters' => $request->filters ?? []
            ]);
        }

        $journeyService = new JourneyService();
        $journeyService->completeStep(Auth::user(), 'search_run');

        return [
            'status' => 'success',
            'search' => $search
        ];
    }

    /*
     * The search results page "/search/contacts"
     */
    public function agencyDomains()
    {
        $displayClientSelect = $this->displayClientSelect;
        $teams = Auth::user()->getAccessibleTeams();
        $campaigns = Campaign::whereIn('team_id', $teams->pluck('id')->toArray())
            ->orderBy('created_at', 'desc')
            ->get();

        $searches = Search::where('agency_id', Auth::user()->agency_id)
            ->orderByDesc('id')
            ->get();

        return view('data-search.emails.index', compact(
            "displayClientSelect", "teams", "campaigns", "searches"
        ));
    }

    /*
     * Return the list of saved domains/contacts
     * TODO: optimise this query
     */
    public function agencyDomainList(Request $request)
    {
        $imported = data_get($request, 'imported', null);
        $bounced = data_get($request, 'bounced', null);
        $replied = data_get($request, 'replied', null);
        $contacted = data_get($request, 'contacted', null);
        $searchId = data_get($request, 'search_id', null);

        // domain filters
        $categoryIds = !empty($request->categories) ? $request->categories : [];
        $countryIds = !empty($request->countries) ? $request->countries : [];
        $saleIds = !empty($request->estimated_sales) ? $request->estimated_sales : [];
        $productCountIds = !empty($request->product_counts) ? $request->product_counts : [];
        $platformIds = !empty($request->platforms) ? $request->platforms : [];
        $appIds = !empty($request->apps) ? $request->apps : [];
        $contactTypeIds = !empty($request->contact_types) ? $request->contact_types : [];
        $currencyIds = !empty($request->currencies) ? $request->currencies : [];
        $featureIds = !empty($request->features) ? $request->features : [];
        $languageIds = !empty($request->domain_languages) ? $request->domain_languages : [];
        $monthIds = !empty($request->months) ? $request->months : [];
        $salesChannelIds = !empty($request->sales_channels) ? $request->sales_channels : [];
        $shippingCarrierIds = !empty($request->shipping_carriers) ? $request->shipping_carriers : [];
        $shippingCountryIds = !empty($request->shipping_countries) ? $request->shipping_countries : [];
        $tagIds = !empty($request->tags) ? $request->tags : [];
        $technologyIds = !empty($request->technologies) ? $request->technologies : [];

        $planIds = !empty($request->plans) ? $request->plans : [];

        // exclude platforms if plans is available because plan is kindof sub-platform filter
        if(!empty($planIds)) {
            $platformIds = [];
        }

        $domains = AgencyDomain::where('agency_id', Auth::user()->agency_id)
            ->ofImported($imported)
            ->ofBounced($bounced)
            ->ofReplied($replied)
            ->ofContacted($contacted)
            ->ofSearch($searchId)
            ->ofDomainCategories($categoryIds)
            ->ofDomainCountries($countryIds)
            ->ofDomainSales($saleIds)
            ->ofDomainProducts($productCountIds)
            ->ofDomainApps($appIds)
            ->ofDomainContactTypes($contactTypeIds)
            ->ofDomainCurrencies($currencyIds)
            ->ofDomainFeatures($featureIds)
            ->ofDomainLanguages($languageIds)
            ->ofDomainMonths($monthIds)
            ->ofDomainPlatforms($platformIds)
            ->ofDomainPlans($planIds)
            ->ofDomainSalesChannels($salesChannelIds)
            ->ofDomainShippingCarriers($shippingCarrierIds)
            ->ofDomainShippingCountries($shippingCountryIds)
            ->ofDomainTags($tagIds)
            ->ofDomainTechnologies($technologyIds)
//            ->ofDomainThemes($themeIds)
//            ->ofDomainThemeVendors($themeVendorIds)
            ->where('email_search_status', 'success')
            ->with([
                'domain.categories',
                'domain.apps',
                'emails',
                'emails.contact:id,email_activity_confidence,external_email_confidence,verified_at,verification_error,source,seniority_id',
                'domain.platform:id,nice_name',
                'domain.domainData:id,data'
            ])
            ->orderBy('id', 'desc')
            ->paginate(20);

        $domains->getCollection()->transform(function ($agencyDomain, $key) {
            $data =  $agencyDomain->domain->domainData->data ?? null;
            $img = $data['og_image'] ?? null;
            $icon = $data['icon'] ?? null;
            $merchantName = $agencyDomain->domain->merchant_name ?? null;

            if(empty($merchantName)) {
                $merchantName = $data['merchant_name'] ?? null;
            }

            if(Str::startsWith($merchantName, 'www.')) {
                $merchantName = ltrim($merchantName, 'www.');
            }

            $agencyDomain->icon = $icon ?? $img;
            $agencyDomain->merchant_name = $merchantName;
            unset($agencyDomain->domain->domainData);

            // add current email
            $currentEmail = $agencyDomain->emails->where('id', $agencyDomain->current_email_id)->first();
            $agencyDomain->current_email = $currentEmail;

            return $agencyDomain;
        });

        return [
            "status" => "success",
            "domains" => $domains
        ];
    }

    /*
     * Update agencyDomain's current email if they want to use different email
     * since the default is the 1st result from anymailfinder
     */
    public function agencyDomainUpdateEmail(AgencyDomain $agencyDomain, Request $request)
    {
        $email = AgencyEmail::where('id', $request->email_id)
            ->where('agency_domain_id', $agencyDomain->id)
            ->first();

        if(empty($email)) {
            return [ "status" => "error" ];
        }

        $agencyDomain->update(['current_email_id' => $email->id]);

        return [ "status" => "success" ];
    }

    /**
     * Returns searches not DONE to be listed in the results page
     */
    public function inprogressSearch()
    {
        $inprogressSearches = Search::whereIn('status', ['INPROGRESS', 'PAUSED', 'WAITING'])
            ->where('agency_id', Auth::user()->agency_id)
            ->select('id', 'agency_id', 'status', 'name', 'current_contacts', 'total_contacts')
            ->orderBy('status', 'asc')
            ->orderBy('id', 'desc')
            ->get();

        return [
            "status" => "success",
            "searches" => $inprogressSearches
        ];
    }

    /**
     * Returns agency_searches to use as filter in results page
     */
    public function listSearch()
    {
        $searches = Search::where('agency_id', Auth::user()->agency_id)
            ->select(
                'id', 'name', 'search_filters', 'agency_id',
                'total_contacts', 'current_contacts',
                'used_credits', 'status', 'created_at'
            )
            ->orderByDesc('id')
            ->get();

        return [
            "status" => "success",
            "searches" => $searches
        ];
    }

    /**
     * Returns available search credits of the agency
     */
    public function getAvailableCredits()
    {
        $agency = Agency::find(Auth::user()->agency_id);
        $creditService = new Wavo3CreditsBillingService($agency);
        $creditsAvailable = $creditService->getAvailableCredits();

        return [
            "status" => "success",
            "credits" => $creditsAvailable
        ];
    }

    /**
     * Returns agency's credit logs for the billing search history
     */
    public function creditLogs()
    {
        $creditLogs = CreditLog::ofAgency(Auth::user()->agency_id)
            ->whereIn('type', ['increment', 'usage'])
            ->orderBy('created_at', 'desc')
            ->paginate(5);

        return [
            "status" => "success",
            "creditLogs" => $creditLogs
        ];
    }

    /**
     * Returns agency's credit stats for the billing search history chart
     */
    public function creditUsageStat(Request $request)
    {
        $dayLabels = [];
        $barLogs = [];

        $statDays = $request->days ? $request->days : 7;
        $startDay = now()->subDays($statDays);
        $endDay = now();

        $creditStats = CreditLog::query()->selectRaw("DATE_FORMAT(created_at, '%Y-%m-%d') as date, SUM(amount) AS amount")
            ->where('type', 'usage')
            ->where('created_at', '>=', $startDay)
            ->ofAgency(Auth::user()->agency_id)
            ->groupBy('date')
            ->get();

        while ($startDay <= $endDay->endOfDay()) {
            $currDay = $startDay->format("Y-m-d");
            $stats = $creditStats->where('date', $currDay)->first();
            array_push($dayLabels, $startDay->format('Md'));

            // barchart
            $currDayBarLogs = $stats ? $stats->amount : 0;
            array_push($barLogs, intval($currDayBarLogs));

            $startDay->addDay();
        }

        return response()->json([
            'status' => 'success',
            'dayLabels' => $dayLabels,
            'barLogs' => $barLogs
        ]);
    }

    /**
     * Process on-demand search
     */
    public function accessEmail(Domain $domain) {
        $agencyId = Auth::user()->agency_id;
        $noResult = [
            "status" => "error",
            "message" => "",
            "contact" => [
                "name" => "",
                "email" => ""
            ]
        ];

        // check if valid to search
        $agencyDomain = AgencyDomain::where("agency_id", $agencyId)
            ->where("domain_id", $domain->id)
            ->with('email')
            ->first();

        if (!empty($agencyDomain)) {
            $contactName = $agencyDomain->email->full_name ?? "";
            $contactEmail = $agencyDomain->email->email ?? "";
            return [
                "status" => "success",
                "message" => "Already saved",
                "contact" => [
                    "name" => $contactName,
                    "email" => $contactEmail
                ],
                "agency_domain" => array(
                    "id" => $agencyDomain->id,
                    "list_ids" => [],
                    "status" => $agencyDomain->email_search_status
                )
            ];
        }

        // lock credit
        $agency = Agency::find($agencyId);
        $creditService = new Wavo3CreditsBillingService($agency);
        $merchantName = $domain->merchant_name ?? $domain->name;
        $creditReason = 'Access Email: '.$merchantName;

        try {
            $creditService->lockCredits(1, $creditReason);
        } catch (\Throwable $e) {
            $lockError = $e->getMessage();

            return response()->json([
                'message' => $lockError,
                'errors' => [
                    'credits' => [
                        $lockError
                    ]
                ]
            ], 422);
        }

        // a placeholder search for search on-demand
        $defaultSearch = Search::firstOrCreate([
            'agency_id' => Auth::user()->agency_id,
            'name' => 'Access Email',
        ], [
            'status' => 'DONE',
            'search_filters' => []
        ]);

        $result = $this->accessEmailProcess($domain, $defaultSearch);

        // process credits
        try {
            if($result['confidence'] == 'verified') {
                $creditService->finalizeLockedCredits(1, $creditReason);
            } else {
                $creditService->releaseLockedCredits(1, $creditReason);
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        }

        return $result;
    }

    /**
     * Helper function
     * process ondemand search using DomainContactsFinderService
     */
    protected function accessEmailProcess(Domain $domain, $defaultSearch) 
    {
        $monthsAgo = now()->subMonths(self::SEARCHED_MONTHS_AGO);
        $contactsQuality = new DomainContactsQualityService();
        $contactFinderService = new DomainContactsFinderService($domain, $defaultSearch);

        $dbResult = $contactFinderService->searchFromDatabase();
        $searchResult = $dbResult;

        if ($contactsQuality->shouldSearchNewContacts($domain, $monthsAgo)) {
            $apiResult = $contactFinderService->searchFromApi();
            $searchResult = $contactFinderService->mergeResults($dbResult, $apiResult);
        }

        $savedResponse = $contactFinderService->saveEmailResults($searchResult);

        // return
        $email = $searchResult['emails'][0]['email'] ?? "";
        $fname = $searchResult['emails'][0]['first_name'] ?? "";
        $lname = $searchResult['emails'][0]['last_name'] ?? "";

        return [
            "status" => $savedResponse["status"] ?? "error",
            "agency_domain" => array(
                "id" => $savedResponse["agency_domain_id"] ?? null,
                "list_ids" => [],
                "status" => "success",
                "contacts_count" => count($searchResult['emails']),
            ),
            "contact" => [
                "name" => trim($fname . " " . $lname),
                "email" => $email
            ],
            "confidence" => $searchResult['confidence'] ?? null
        ];
    }

    /*
     * Returns the list of all searchable domains for the agency
     * Excludes domains that the agency has already saved
     */
    public function searchPaginatedDomains(Request $request)
    {
        // max page should be 100
        if($request->page && intval($request->page) > 100) {
            $request->merge([ 'page' => 100 ]);
        }

        $perPage = 50;
        $currPage = $request->page;
        $offset = ($currPage -1) * $perPage;

        $filters = DomainFilterNormalizer::normalize($request->all());
        // dd($filters);

        $agencyId = Auth::user()->agency_id;
        $teamIds = Auth::user()->getAccessibleTeams()->pluck('id')->toArray();

        $domains = Domain::select('id', 'name', 'employee_count_id', 'estimated_sales_id',
                'product_count_id', 'region_id', 'country_id', 'platform_id',
                'plan_id', 'merchant_name',
            )
            ->filter($filters, $filters['viewType'] ?? null, $agencyId, $teamIds)
            ->orderBy('id')
            ->with([
                'categories:id,name',
                'country:id,name,code',
                'platform:id,nice_name',
                'domainData:id,data',
                'agencyDomains' => function($q) use($agencyId) {
                    $q->where('agency_id', $agencyId)
                        ->with(['email', 'agencyLists' => function($q) {
                            $q->where('user_id', Auth::user()->id);
                        }])
                        ->withCount(['emails']);
                },
            ])
            ->skip($offset)
            ->take($perPage)
            ->cacheFor(Domain::CACHE_FOR)
            ->get();

        // $domains = new LengthAwarePaginator($domains, 2000, $perPage, $currPage);

        $domains->transform(function ($domain, $key) {
            $data =  $domain->domainData->data ?? null;
            $img = $data['og_image'] ?? null;
            $icon = $data['icon'] ?? null;
            $merchantName = $domain->merchant_name ?? null;
            $socialContacts = null;

            if(!empty($data['contact_info'])) {
                foreach ($data['contact_info'] as $contact) {
                    $socialContacts[$contact['type']] = $contact['value'];
                }
            }

            if(empty($merchantName)) {
                $merchantName = $data['merchant_name'] ?? null;
            }

            if(Str::startsWith($merchantName, 'www.')) {
                $merchantName = ltrim($merchantName, 'www.');
            }

            $domain->icon = $icon ?? $img;
            $domain->merchant_name = $merchantName;
            $domain->contacts = $socialContacts;
            unset($domain->domainData);

            // display censored email (fake) for consistency
            // $domainParts = explode('.', urlTrim($domain->name));
            // $mainDomain = implode('.', array_slice($domainParts, -2));
            $mainDomain = getMainDomain(urlTrim($domain->name));
            $emailName = "";
            $emailCharCount = 3;
            $nameLength = strlen($merchantName);

            // dynamic length of asterisk based on merchant name length
            if ($nameLength >= 20) {
                $emailCharCount = 5;
            } else if ($nameLength < 20 && $nameLength >= 10) {
                $emailCharCount = intval(ceil($nameLength / 3));
            } else if ($nameLength < 10 && $nameLength >= 4) {
                $emailCharCount = intval(ceil($nameLength / 2));
            } else {
                $emailCharCount = $nameLength * 2;
            }

            for ($i=0; $i < $emailCharCount; $i++) {
                $emailName = $emailName."*";
            }

            $domain->contact_email = $emailName."@".$mainDomain;

            // check if domain is already saved by this agency
            $listIds = [];
            $agencyDomain = null;

            if($domain->agencyDomains->count()) {
                $ad = $domain->agencyDomains->first();

                $agencyDomain = array(
                    'id' => $ad->id,
                    'status' => $ad->email_search_status,
                    'list_ids' => array(),
                    'contacts_count' => $ad->emails_count
                );

                // transform agency_lists
                if($ad->agencyLists->count()) {
                    $agencyDomain['list_ids'] = $ad->agencyLists->pluck('id')->toArray();
                }

                // display contact's email
                if(!empty($ad->email->email)) {
                    $domain->contact_email = $ad->email->email;
                }
            } else {
                // $domain->has_agency_domain = false;
            }

            unset($domain->agencyDomains);

            // $domain->agency_domain_id = $agencyDomainId;
            $domain->agency_domain = $agencyDomain;
            $domain->list_ids = $listIds;


            // for vue state
            $domain->is_fetching = false;
            $domain->is_saving = false;

            return $domain;
        });

        // If no domains found, then do no more filtering
        if ($domains->isEmpty()) {
            info('no domains returned');

            return [
                "status" => "success",
                "domains" => $domains,
                'hidden' => 0,
            ];
        }

        // return view('style-guide.index');

        return response()->json([
            "status" => "success",
            // "domains" => $domains->pluck('name'),
            "domains" => $domains,
            'hidden' => 0,
        ], 200);
    }
}
