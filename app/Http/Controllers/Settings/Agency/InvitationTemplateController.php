<?php

namespace App\Http\Controllers\Settings\Agency;

use App\Agency;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class InvitationTemplateController extends Controller
{
    /**
     * @param  \Illuminate\Http\Request $request
     * @param  \App\Agency $agency
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request, Agency $agency)
    {
        return response()->json([
            'invitation_template' => $agency->getDefaultInvitationTemplate()
        ]);
    }
}
