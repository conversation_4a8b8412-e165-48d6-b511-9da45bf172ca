<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\StoreLeads\PlatformHost;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PlatformHostController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:support');
    }

    /**
     * Display the platform hosts management page
     */
    public function index()
    {
        $platformHosts = PlatformHost::whereNull('is_platform')
            ->orderBy('count_subdomains', 'desc')
            ->limit(20)
            ->get();

        // Load related domains for each platform host
        $platformHosts->each(function ($platformHost) {
            $platformHost->related_domains = $platformHost->getRelatedDomains(3);
        });

        return view('admin.platform-hosts.index', [
            'platformHosts' => $platformHosts
        ]);
    }

    /**
     * Update the is_platform field via AJAX
     */
    public function updatePlatformStatus(Request $request): JsonResponse
    {
        $request->validate([
            'id' => 'required|integer|exists:sl_platform_hosts,id',
            'is_platform' => 'required|boolean'
        ]);

        $platformHost = PlatformHost::findOrFail($request->id);
        $platformHost->is_platform = $request->is_platform;
        $platformHost->save();

        return response()->json([
            'success' => true,
            'message' => 'Platform status updated successfully',
            'data' => [
                'id' => $platformHost->id,
                'is_platform' => $platformHost->is_platform
            ]
        ]);
    }

    /**
     * Load more platform hosts via AJAX
     */
    public function loadMore(Request $request): JsonResponse
    {
        $offset = $request->get('offset', 0);
        
        $platformHosts = PlatformHost::whereNull('is_platform')
            ->orderBy('count_subdomains', 'desc')
            ->offset($offset)
            ->limit(20)
            ->get();

        // Load related domains for each platform host
        $platformHosts->each(function ($platformHost) {
            $platformHost->related_domains = $platformHost->getRelatedDomains(3);
        });

        return response()->json([
            'success' => true,
            'data' => $platformHosts,
            'has_more' => $platformHosts->count() === 20
        ]);
    }
}
