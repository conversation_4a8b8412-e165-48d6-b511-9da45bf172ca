<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CampaignStageStats extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'number' => $this->number,
            'sent' => $this->emails_sent,
            'queued' => $this->emails_to_send,
            'contacted' => $this->emails_delivered,
            'replied' => $this->replies,
            'positive' => $this->positive_count,
            'bounced' => $this->bounced,
            'messages_clicked' => $this->clicked,
            'messages_opened' => $this->opened,
            'email_templates' => EmailTemplateStats::collection($this->whenLoaded('emailTemplates')),
        ];
    }
}
