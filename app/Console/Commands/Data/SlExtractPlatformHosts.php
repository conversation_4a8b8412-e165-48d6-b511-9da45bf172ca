<?php

namespace App\Console\Commands\Data;
use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\Platform;
use App\Models\StoreLeads\PlatformHost;
use App\Services\DomainNormalizationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SlExtractPlatformHosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:sl-extract-platform-hosts {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Extract platform hosts from domains';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $id = $this->argument('id') ?? 0;
        $domainNormalizer = app(DomainNormalizationService::class);

        Log::info('extract-platform-hosts: Start at id: '.$id);
        $this->info('extract-platform-hosts: Start at id: '.$id);

        // Array to count root domains in memory
        $rootDomainCounts = [];
        $processedCount = 0;
        $errorCount = 0;

        // Process domains in larger chunks for better performance
        Domain::select('id', 'name') // Only fetch necessary fields for better performance
            ->where('name', 'not like', 'www.%') // not starting with www
            ->whereRaw('CHAR_LENGTH(name) - CHAR_LENGTH(REPLACE(name, ".", "")) >= 2') // only if it has at least 2 dots
            ->where('id', '>', $id)
            ->chunk(10000, function ($domains) use (&$rootDomainCounts, &$processedCount, &$errorCount, $domainNormalizer) {
                foreach ($domains as $domain) {
                    $url = urlTrim($domain->name); // remove http and www

                    try {
                        $normalized = $domainNormalizer->parseAndNormalize($url);
                        $root = $normalized['root_domain'];

                        if (!empty($root)) {
                            $rootDomainCounts[$root] = ($rootDomainCounts[$root] ?? 0) + 1;
                        }
                        $processedCount++;
                    } catch (\Throwable $e) {
                        $errorCount++;
                        Log::error('extract-platform-hosts: Failed on domain: '.$domain->name.' - '.$e->getMessage());
                    }
                }

                Log::info('extract-platform-hosts: Processed chunk ending at id: '.$domains->last()->id.' (Total processed: '.$processedCount.', Errors: '.$errorCount.')');
                $this->info('extract-platform-hosts: Processed chunk ending at id: '.$domains->last()->id.' (Total processed: '.$processedCount.', Errors: '.$errorCount.')');
            });

        Log::info('extract-platform-hosts: Finished processing domains. Found '.count($rootDomainCounts).' unique root domains');
        $this->info('extract-platform-hosts: Finished processing domains. Found '.count($rootDomainCounts).' unique root domains');

        // Get existing platform hosts to avoid duplicates
        $existingHosts = PlatformHost::pluck('registrable')->toArray();
        Log::info('extract-platform-hosts: Found '.count($existingHosts).' existing platform hosts');
        $this->info('extract-platform-hosts: Found '.count($existingHosts).' existing platform hosts');

        // Prepare batch insert data
        $platformHosts = [];
        $newHostsCount = 0;

        foreach ($rootDomainCounts as $root => $count) {
            if ($count > 10 && !in_array($root, $existingHosts)) {
                $confidence = $this->calculateConfidence($count);

                $platformHosts[] = [
                    'registrable' => $root,
                    'count_subdomains' => $count,
                    'confidence' => $confidence,
                    'is_platform' => null,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
                $newHostsCount++;
            }
        }

        // Batch insert all platform hosts
        if (!empty($platformHosts)) {
            // Insert in batches to avoid memory issues with very large datasets
            $batchSize = 1000;
            $batches = array_chunk($platformHosts, $batchSize);

            foreach ($batches as $batch) {
                PlatformHost::insert($batch);
            }

            Log::info('extract-platform-hosts: Inserted '.$newHostsCount.' new platform hosts');
            $this->info('extract-platform-hosts: Inserted '.$newHostsCount.' new platform hosts');
        } else {
            Log::info('extract-platform-hosts: No new platform hosts to insert');
        }

        Log::info('extract-platform-hosts: Completed successfully');
        $this->info('extract-platform-hosts: Completed successfully');
    }

    /**
     * Calculate confidence score based on subdomain count
     *
     * @param int $count
     * @return int
     */
    private function calculateConfidence(int $count): int
    {
        if ($count > 250) return 100;
        if ($count > 150) return 90;
        if ($count > 100) return 80;
        if ($count > 50) return 70;
        return 0;
    }
}
