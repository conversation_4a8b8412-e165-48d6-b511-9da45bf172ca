<?php

namespace App\Console\Commands\Jobs;

use App\EmailAccount;
use App\EmailEngineWebhook;
use App\Jobs\Email\EmailMessageSync;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckEEWebhooksCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobs:check-ee-webhooks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch jobs to process EE webhooks saved to db.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        EmailEngineWebhook::notProcessed()->pluck('account_id')->unique()->each(function($accountId) {
            $emailAccount = EmailAccount::findHash($accountId);
            if ($emailAccount) {
                info('Dispatch message sync for emailAccount-'.$emailAccount->id);
                EmailMessageSync::dispatch($emailAccount)->onQueue('api');
            }
        });
    }
}
