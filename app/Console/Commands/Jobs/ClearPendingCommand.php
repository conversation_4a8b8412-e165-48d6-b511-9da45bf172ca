<?php

namespace App\Console\Commands\Jobs;

use Illuminate\Console\Command;
use Illuminate\Redis\RedisManager;

class ClearPendingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobs:clear-pending
                            {queue : The queue to clear}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove pending jobs of specified queue';

    /**
     * Execute the console command.
     *
     * @param RedisManager $redis
     * @return mixed
     */
    public function handle(RedisManager $redis)
    {
        if (is_null($this->argument('queue'))) {
            $this->error('No queue provided');

            return 1;
        }

        $queue = $this->argument('queue');
        info("queues:$queue");
        info("queues:$queue:delayed");

        $redis->del("queues:$queue");
        $redis->del("queues:$queue:delayed");
    }
}
