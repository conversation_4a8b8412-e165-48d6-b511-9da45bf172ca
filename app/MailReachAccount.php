<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class MailReachAccount extends Model
{
    protected $guarded = ['id'];

    protected $appends = ['enabled'];

    protected $casts = ['domain' => 'json'];

    /**
     * Each EmailEngine Account belongs to an email account.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo(EmailAccount::class);
    }

    public function isEnabled()
    {
        return (bool) $this->alive && !$this->suspended && $this->warming_enabled;
    }

    public function getEnabledAttribute()
    {
        return (bool) $this->alive && !$this->suspended && $this->warming_enabled;
    }
}
