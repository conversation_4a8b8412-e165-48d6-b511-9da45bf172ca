<?php

namespace App\Jobs;

use App\Models\StoreLeads\App;
use App\Models\StoreLeads\Category;
use App\Models\StoreLeads\ContactType;
use App\Models\StoreLeads\Country;
use App\Models\StoreLeads\Currency;
use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\Feature;
use App\Models\StoreLeads\ImportBatch;
use App\Models\StoreLeads\Language;
use App\Models\StoreLeads\Month;
use App\Models\StoreLeads\Platform;
use App\Models\StoreLeads\SalesChannel;
use App\Models\StoreLeads\ShippingCarrier;
use App\Models\StoreLeads\ShippingCountry;
use App\Models\StoreLeads\Tag;
use App\Models\StoreLeads\Technology;
use App\Models\StoreLeads\Theme;
use App\Models\StoreLeads\ThemeVendor;
use App\Models\StoreLeads\Week;
use App\Traits\LogsMessages;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class StoreLeadsImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $timeout = 1860;
    public $backoff = 30;
    public $tries = 5;


    public $filename;
    public $start;
    public $batchId;
    public $maxRow;

    protected const JOB_BATCH_SIZE = 1000;
    protected const UPSERT_BATCH_SIZE = 100;
    protected const FILE_SPLIT_SIZE = 1000000;

    private $weeks;
    private $months;
    private $categories;
    private $apps;
    private $technologies;
    private $contactTypes;
    private $features;
    private $countries;
    private $currencies;
    private $tags;
    private $languages;
    private $salesChannels;
    private $shipplingCountries;
    private $shippingCarriers;
    private $themeVendors;
    private $themes;
    private $platforms;


    public function __construct($filename, $start = 0, $batchId = 0)
    {
        $this->filename = $filename;
        $this->batchId = $batchId;
        $this->start = $start;
//        $this->maxRow = $batchId * self::FILE_SPLIT_SIZE;

        $this->logChannel = config('logging.default');
        $this->logPrefix = "SL-Import-$batchId-$start-";
    }

    public function tags()
    {
        return ['store-leads', 'sl-import-file-'.$this->filename, 'sl-import-batch-'.$this->batchId];
    }

    public function middleware()
    {
        return [
            (new WithoutOverlapping("sl-import-batch-{$this->batchId}"))
                ->dontRelease()
                ->expireAfter(1900),
        ];
    }

    public function handle()
    {
        $this->logInfo('Start StoreLeadsImport job at line: '. $this->start.', batch-id: '.$this->batchId. ', file: '.$this->filename);

        if (empty($this->filename)) {
            $this->fail('No filename given');
        }

        // Insert or update ImportBatch with batch_id = $this->batchId
        $importBatch = ImportBatch::on('mysql::write')->updateOrCreate(
            ['batch_id' => $this->batchId],
            [
                'start_row' => $this->start,
                'next_start_row' => null,
                'is_complete' => false,
                'job_started_at' => now(),
                'job_ended_at' => null
            ]
        );

        $this->weeks = Week::on('mysql::write')->get();
        $this->months = Month::on('mysql::write')->get();
        $this->categories = Category::on('mysql::write')->get();
        $this->apps = App::on('mysql::write')->get();
        $this->technologies = Technology::on('mysql::write')->get();
        $this->contactTypes = ContactType::on('mysql::write')->get();
        $this->features = Feature::on('mysql::write')->get();
        $this->countries = Country::on('mysql::write')->get();
        $this->currencies = Currency::on('mysql::write')->get();
        $this->tags = Tag::on('mysql::write')->get();
        $this->languages = Language::on('mysql::write')->get();
        $this->salesChannels = SalesChannel::on('mysql::write')->get();
        $this->shipplingCountries = ShippingCountry::on('mysql::write')->get();
        $this->shippingCarriers = ShippingCarrier::on('mysql::write')->get();
        $this->themeVendors = ThemeVendor::on('mysql::write')->get();
        $this->themes = Theme::on('mysql::write')->get();
        $this->platforms = Platform::on('mysql::write')->get();

        $filePath = storage_path("/app/sl-export/{$this->filename}");

        if (Str::endsWith($this->filename, '.gz')) {
            $dataFile = new \SplFileObject("compress.zlib://$filePath", 'r');
        } else {
            $dataFile = new \SplFileObject($filePath, 'r');
        }

        if (!$dataFile) {
            $this->fail('Failed to open the file');
        }

        try {
            $dataFile->seek($this->start);
        } catch (\Throwable $e) {
            $this->logError('Failed to seek to the start position');
            throw $e;
        }
        $this->logInfo("Skipped to line: $this->start");

        $i = 1;
        $domainsBatch = [];
        $pivotDataBatch = [];
        $jsonDataBatch = [];

        if ($dataFile->eof()) {
            $this->logInfo('End of file reached. Import Complete!'.' batch-id: '.$this->batchId. ', file: '.$this->filename);
            $dataFile = null;
            $importBatch->update(['is_complete' => true, 'job_ended_at' => now()]);

            return;
        }

        while (!$dataFile->eof() && $i <= self::JOB_BATCH_SIZE) {
            $line = $dataFile->fgets();

            if ($line !== false && !empty($line)) {
                $data = json_decode($line, true);

                if (empty($data['name'])) {
                    $this->logError("Domain name is missing in line $i");
                    $i++;
                    $dataFile->next();
                    continue;
                }

                $oldDomain = Domain::on('mysql::write')->where('name', $data['name'])->first();
                if ($oldDomain && $oldDomain->domainData()->exists()) {
                    $domainData = $oldDomain->domainData->data;
                    if (!empty($domainData['last_updated_at']) && Carbon::parse($domainData['last_updated_at'])->timestamp === Carbon::parse($data['last_updated_at'])->timestamp) {
                        $i++;
                        $dataFile->next();
                        continue;
                    }
                }

                if ($oldDomain) {
                    $isUpdate = true;
//                    $this->logInfo("Update domain: {$data['name']}");
                } else {
                    $isUpdate = false;
                }

                try {
                    $monthId = $this->getMonthId($data);
                    $weekId = $this->getWeekId($data);
                    $platformId = $this->getPlatformId($data);
//                    $statusId = 1; // only active domains are imported
                    $categoryIds = $this->getCategoryIds($data);
                    $appIds = $this->getAppIds($data);
                    $technologyIds = $this->getTechnologyIds($data);
                    $contactTypeIds = $this->getContactTypeIds($data);
                    $employeeCountId = $this->getIdFromArrayCount(Domain::EMPLOYEE_COUNTS, $data, 'employee_count');
                    $estimatedSalesId = $this->getIdFromArrayCount(Domain::ESTIMATED_SALES, $data, 'estimated_sales');
//                    $planId = $this->getIdFromArray(Domain::PLANS, $data, 'plan');
                    $productCountId = $this->getIdFromArrayCount(Domain::PRODUCT_COUNTS, $data, 'product_count');
                    $featureIds = $this->getFeatureIds($data);
                    $regionId = $this->getIdFromArray(Domain::REGIONS, $data, 'region');
                    $countryId = $this->getCountryId($data);
                    $currencyId = $this->getCurrencyId($data);
                    $tagIds = $this->getTagIds($data);
                    $languageId = $this->getLanguageId($data);
                    $domainTypeId = $this->getDomainTypeId($data);
                    // $topLevelDomainId = $this->getTopLevelDomainId($data['name']); TODO: Find a way to get the .tld for the domain name (use a list?)
                    $salesChannelIds = $this->getSalesChannelIds($data);
                    $shippingCountryIds = $this->getShippingCountryIds($data);
                    $shippingCarrierIds = $this->getShippingCarrierIds($data);
                    $themeVendorId = $this->getThemeVendorId($data);
                    $themeId = $this->getThemeId($data);

                    if (!empty($data['apps'])) {
                        // Keep only 'name' from apps arrays
                        $data['apps'] = array_map(function ($app) {
                            return $app['name'];
                        }, $data['apps']);
                    }
                    if (!empty($data['technologies'])) {
                        // Keep only 'name' from technologies arrays
                        $data['technologies'] = array_map(function ($technology) {
                            return $technology['name'];
                        }, $data['technologies']);
                    }

                } catch (\Throwable $e) {
                    $this->logError("Error parsing line $i");
                    $this->logError($e->getMessage());

                    throw $e;
                }

                $domainData = [
                    'name' => $data['name'],
//                    'title' => mb_substr($title, 0, 255, 'UTF-8'),
                    'week_id' => $weekId,
                    'month_id' => $monthId,
                    'platform_id' => $platformId,
//                    'status_id' => $statusId,
                    'employee_count_id' => $employeeCountId,
                    'estimated_sales_id' => $estimatedSalesId,
//                    'plan_id' => $planId,
                    'product_count_id' => $productCountId,
                    'region_id' => $regionId,
                    'country_id' => $countryId,
                    'currency_id' => $currencyId,
                    'language_id' => $languageId,
                    'domain_type_id' => $domainTypeId,
//                    'top_level_domain_id' => $topLevelDomainId,
                    'theme_vendor_id' => $themeVendorId,
                    'theme_id' => $themeId,
                ];

                $domainsBatch[] = $domainData;
                $pivotDataBatch[] = [
                    'name' => $data['name'],
                    'update' => $isUpdate,
                    'categories' => $categoryIds,
                    'apps' => $appIds,
                    'technologies' => $technologyIds,
                    'contactTypes' => $contactTypeIds,
                    'features' => $featureIds,
                    'tags' => $tagIds,
                    'salesChannels' => $salesChannelIds,
                    'shippingCountries' => $shippingCountryIds,
                    'shippingCarriers' => $shippingCarrierIds,
                ];

                $jsonDataBatch[] = [
                    'id' => null,
                    'name' => $data['name'],
                    'data' => json_encode($data),
                ];
            }

            if ($i % self::UPSERT_BATCH_SIZE == 0) {
                // Insert or update the domains in batch
                if (!empty($domainsBatch)) {
                    $this->logInfo("Import data to db. L: ".($i+$this->start));
                    $this->importBatch($domainsBatch, $pivotDataBatch, $jsonDataBatch);
                } else {
                    $this->logInfo("Skip to line:".($i+$this->start));
                }
                // Reset the batches
                $domainsBatch = [];
                $pivotDataBatch = [];
                $jsonDataBatch = [];
            }

            // Every 500 records, log the progress
            if ($i % 1000 == 0) {
                $this->logInfo("Processed $i records");
            }

            $i++;
            $dataFile->next();
        }

        $i--;
        $dataFile = null;

        if (!empty($domainsBatch)) {
            $this->logInfo("Import data to db. L: ".($i+$this->start));
            $this->importBatch($domainsBatch, $pivotDataBatch, $jsonDataBatch);
            $domainsBatch = [];
            $pivotDataBatch = [];
            $jsonDataBatch = [];
        }

        $nextRow = $this->start + $i;
        $this->logInfo('Dispatch self to start at line: ' . $nextRow . ', batch-id: ' . $this->batchId . ', file: ' . $this->filename);
        $importBatch->update([
            'next_start_row' => $nextRow,
            'job_ended_at' => now()
        ]);

        self::dispatch($this->filename, $nextRow, $this->batchId)
            ->onQueue('browser')
            ->delay(now()->addSeconds(10));
    }


    protected function getIdFromArray($list, $data, $key)
    {
        if (empty($data[$key])) {
            return 0;
        }

        $id = array_search(strtolower($data[$key]), $list);

        if (!$id) {
            throw new \Exception("{$data[$key]} not found in list {$key}s.");
        }

        return $id;
    }

    protected function getIdFromArrayCount($list, $data, $key)
    {
        if (empty($data[$key])) {
            return 0;
        }

        $id = null;
        foreach($list as $val => $item) {
            if($item['min'] <= $data[$key] && $item['max'] >= $data[$key]) {
                $id = $val;
                break;
            }
        }

        if ($id === null) {
            throw new \Exception("{$data[$key]} not found in list {$key}s.");
        }

        return $id;
    }

    protected function getWeekId($data)
    {
        $weekDate = Carbon::parse($data['created_at'])->format('Y-m-d');

        $week = $this->weeks->where('name', $weekDate)->first();

        if (!$week) {
            $monthId= $this->getMonthId($data);
            try {
                $week = new Week();
                $week->name = $weekDate;
                $week->month_id = $monthId;
                $week->save();
            } catch (UniqueConstraintViolationException $e) {
                $week = Week::where('name', $weekDate)->first();
            }
            $this->weeks = Week::all();
        }

        return $week->id;
    }

    protected function getMonthId($data)
    {
        $monthDate = Carbon::parse($data['created_at'])->format('Y-m');

        $month = $this->months->where('name', $monthDate)->first();

        if (!$month) {
            try {
                $month = new Month();
                $month->name = $monthDate;
                $month->save();
            } catch (UniqueConstraintViolationException $e) {
                $month = Month::on('mysql::write')->where('name', $monthDate)->first();
            }
            $this->months = Month::on('mysql::write')->get();
        }

        return $month->id;
    }

    protected function getCategoryIds($data)
    {
        if (empty($data['categories'])) {
            return null;
        }

        $categoryIds = [];

        foreach ($data['categories'] as $categoryPath) {
            $categoryNames = explode("/", trim($categoryPath, "/"));

            $parentId = null;
            foreach ($categoryNames as $categoryName) {
                // Check if the category already exists
                $category = $this->categories->where('name', $categoryName)
                    ->where('parent_id', $parentId)
                    ->first();

                // If not, insert it
                if (!$category) {
                    try {
                        $category = new Category();
                        $category->name = $categoryName;
                        $category->parent_id = $parentId;
                        $category->save();
                    } catch (UniqueConstraintViolationException $e) {
                        $category = Category::on('mysql::write')
                            ->where('name', $categoryName)
                            ->where('parent_id', $parentId)
                            ->first();
                    }
                    $this->categories = Category::on('mysql::write')->get();
                }

                $categoryIds[] = $category->id;

                // Update parentId for the next iteration
                $parentId = $category->id;
            }
        }

        if (empty($categoryIds)) {
            $categoryIds[] = $this->categories->where('name', 'None')->first()->id;
        }

        return collect($categoryIds)->unique()->values()->all();
    }

    protected function getAppIds($data)
    {
        if (empty($data['apps'])) {
            return null;
        }

        $appIds = [];

        foreach ($data['apps'] as $appData) {
            $app = $this->apps->where('name', $appData['name'])->first();

            if (!$app) {
                try {
                    $app = new App();
                    $app->name = $appData['name'];
                    $app->save();
                } catch (UniqueConstraintViolationException $e) {
                    $app = App::on('mysql::write')->where('name', $appData['name'])->first();
                }
                $this->apps = App::on('mysql::write')->get();
            }

            $appIds[] = $app->id;
        }

        return collect($appIds)->unique()->values()->all();
    }

    protected function getTechnologyIds($data)
    {
        if (empty($data['technologies'])) {
            return null;
        }

        $technologyIds = [];

        foreach ($data['technologies'] as $technologyData) {
            $technology = $this->technologies->where('name', $technologyData['name'])->first();

            if (!$technology) {
                try {
                    $technology = new Technology();
                    $technology->name = $technologyData['name'];
                    $technology->save();
                } catch (UniqueConstraintViolationException $e) {
                    $technology = Technology::on('mysql::write')->where('name', $technologyData['name'])->first();
                }
                $this->technologies = Technology::on('mysql::write')->get();
            }

            $technologyIds[] = $technology->id;
        }

        return collect($technologyIds)->unique()->values()->all();
    }

    protected function getContactTypeIds($data)
    {
        if (empty($data['contact_info'])) {
            return null;
        }

        $contactTypeIds = [];

        foreach ($data['contact_info'] as $contactInfo) {
            $contactType = $this->contactTypes->where('name', $contactInfo['type'])->first();

            if (!$contactType) {
                try {
                    $contactType = new ContactType();
                    $contactType->name = $contactInfo['type'];
                    $contactType->save();
                } catch (UniqueConstraintViolationException $e) {
                    $contactType = ContactType::on('mysql::write')->where('name', $contactInfo['type'])->first();
                }
                $this->contactTypes = ContactType::on('mysql::write')->get();
            }

            $contactTypeIds[] = $contactType->id;
        }

        return collect($contactTypeIds)->unique()->values()->all();
    }

    protected function getFeatureIds($data)
    {
        if (empty($data['features'])) {
            return null;
        }

        $featureIds = [];

        foreach ($data['features'] as $featureName) {
            $feature = $this->features->where('name', $featureName)->first();

            if (!$feature) {
                try {
                    $feature = new Feature();
                    $feature->name = $featureName;
                    $feature->save();
                } catch (UniqueConstraintViolationException $e) {
                    $feature = Feature::on('mysql::write')->where('name', $featureName)->first();
                }
                $this->features = Feature::on('mysql::write')->get();
            }

            $featureIds[] = $feature->id;
        }

        return collect($featureIds)->unique()->values()->all();
    }

    protected function getCountryId($data)
    {
        if (empty($data['country_code'])) {
            return null;
        }

        $country = $this->countries->where('code', $data['country_code'])->first();

        if (!$country) {
            try {
                $country = new Country();
                $country->code = $data['country_code'];
                // TODO: Also get the country name
                $country->save();
            } catch (UniqueConstraintViolationException $e) {
                $country = Country::on('mysql::write')->where('code', $data['country_code'])->first();
            }
            $this->countries = Country::on('mysql::write')->get();
        }

        return $country->id;
    }

    protected function getCurrencyId($data)
    {
        if (empty($data['currency_code'])) {
            return null;
        }

        $currency = $this->currencies->where('name', $data['currency_code'])->first();

        if (!$currency) {
            try {
                $currency = new Currency();
                $currency->name = $data['currency_code'];
                $currency->save();
            } catch (UniqueConstraintViolationException $e) {
                $currency = Currency::on('mysql::write')->where('name', $data['currency_code'])->first();
            }
            $this->currencies = Currency::on('mysql::write')->get();
        }

        return $currency->id;
    }

    protected function getTagIds($data)
    {
        if (empty($data['tags'])) {
            return null;
        }

        $tagIds = [];

        foreach ($data['tags'] as $tagName) {
            $tag = $this->tags->where('name', $tagName)->first();

            if (!$tag) {
                try {
                    $tag = new Tag();
                    $tag->name = $tagName;
                    $tag->save();
                } catch (UniqueConstraintViolationException $e) {
                    $tag = Tag::on('mysql::write')->where('name', $tagName)->first();
                }
                $this->tags = Tag::on('mysql::write')->get();
            }

            $tagIds[] = $tag->id;
        }

        return collect($tagIds)->unique()->values()->all();
    }

    protected function getLanguageId($data)
    {
        if (empty($data['language_code'])) {
            return null;
        }

        $language = $this->languages->where('code', $data['language_code'])->first();

        if (!$language) {
            try {
                $language = new Language();
                $language->code = $data['language_code'];
                // TODO: Also get the language name
                $language->save();
            } catch (UniqueConstraintViolationException $e) {
                $language = Language::on('mysql::write')->where('code', $data['language_code'])->first();
            }
            $this->languages = Language::on('mysql::write')->get();
        }

        return $language->id;
    }

    protected function getDomainTypeId($data)
    {
        if (empty($data['platform_domain']) || ($data['platform_domain'] != $data['name'])) {
            $domainType = 'custom';
        } else {
            $domainType = 'platform domain';
        }

        return $this->getIdFromArray(Domain::DOMAIN_TYPES, ['domain_type' => $domainType], 'domain_type');
    }

    protected function getSalesChannelIds($data)
    {
        if (empty($data['sales_channels'])) {
            return null;
        }

        $salesChannelIds = [];

        foreach ($data['sales_channels'] as $salesChannelName) {
            $salesChannel = $this->salesChannels->where('name', $salesChannelName)->first();

            if (!$salesChannel) {
                try {
                    $salesChannel = new SalesChannel();
                    $salesChannel->name = $salesChannelName;
                    $salesChannel->save();
                } catch (UniqueConstraintViolationException $e) {
                    $salesChannel = SalesChannel::on('mysql::write')->where('name', $salesChannelName)->first();
                }
                $this->salesChannels = SalesChannel::on('mysql::write')->get();
            }

            $salesChannelIds[] = $salesChannel->id;
        }

        return collect($salesChannelIds)->unique()->values()->all();
    }

    protected function getShippingCountryIds($data)
    {
        if (empty($data['ships_to_countries'])) {
            return null;
        }

        $shippingCountryIds = [];

        foreach ($data['ships_to_countries'] as $shippingCountryName) {
            $shippingCountry = $this->shipplingCountries->where('name', $shippingCountryName)->first();

            if (!$shippingCountry) {
                try {
                    $shippingCountry = new ShippingCountry();
                    $shippingCountry->name = $shippingCountryName;
                    // TODO: Also fetch the country code
                    $shippingCountry->save();
                } catch (UniqueConstraintViolationException $e) {
                    $shippingCountry = ShippingCountry::on('mysql::write')->where('name', $shippingCountryName)->first();
                }
                $this->shipplingCountries = ShippingCountry::on('mysql::write')->get();
            }

            $shippingCountryIds[] = $shippingCountry->id;
        }

        return collect($shippingCountryIds)->unique()->values()->all();
    }

    protected function getShippingCarrierIds($data)
    {
        if (empty($data['shipping_carriers'])) {
            return null;
        }

        $shippingCarrierIds = [];

        foreach ($data['shipping_carriers'] as $shippingCarrierName) {
            $shippingCarrier = $this->shippingCarriers->where('name', $shippingCarrierName)->first();

            if (!$shippingCarrier) {
                try {
                    $shippingCarrier = new ShippingCarrier();
                    $shippingCarrier->name = $shippingCarrierName;
                    $shippingCarrier->save();
                } catch (UniqueConstraintViolationException $e) {
                    $shippingCarrier = ShippingCarrier::on('mysql::write')->where('name', $shippingCarrierName)->first();
                }
                $this->shippingCarriers = ShippingCarrier::on('mysql::write')->get();
            }

            $shippingCarrierIds[] = $shippingCarrier->id;
        }

        return collect($shippingCarrierIds)->unique()->values()->all();
    }

    protected function getThemeVendorId($data)
    {
        if (empty($data['theme']) || empty($data['theme']['vendor'])) {
            return null;
        }

        $themeVendor = $this->themeVendors->where('name', $data['theme']['vendor'])->first();
        $themeVendorName = mb_substr($data['theme']['vendor'], 0, 255, 'UTF-8');

        if (!$themeVendor) {
            try {
                $themeVendor = new ThemeVendor();
                $themeVendor->name = $themeVendorName;
                $themeVendor->save();
            } catch (UniqueConstraintViolationException $e) {
                $themeVendor = ThemeVendor::on('mysql::write')->where('name', $themeVendorName)->first();
            }
            $this->themeVendors = ThemeVendor::on('mysql::write')->get();
        }

        return $themeVendor->id;
    }

    protected function getThemeId($data)
    {
        if (empty($data['theme']) || empty($data['theme']['name'])) {
            return null;
        }

        $theme = $this->themes->where('name', $data['theme']['name'])->first();
        $themeName = mb_substr($data['theme']['name'], 0, 255, 'UTF-8');

        if (!$theme) {
            try {
                $theme = new Theme();
                $theme->name = $themeName;
                $theme->save();
            } catch (UniqueConstraintViolationException $e) {
                $theme = Theme::on('mysql::write')->where('name', $themeName)->first();
            }
            $this->themes = Theme::on('mysql::write')->get();
        }

        return $theme->id;
    }

    protected function getPlatformId($data)
    {
        if (empty($data['platform'])) {
            return null;
        }

        $platform = $this->platforms->where('name', $data['platform'])->first();

        if (!$platform) {
            try {
                $platform = new Platform();
                $platform->name = $data['platform'];
                $platform->save();
            } catch (UniqueConstraintViolationException $e) {
                $platform = Platform::on('mysql::write')->where('name', $data['platform'])->first();
            }
            $this->platforms = Platform::on('mysql::write')->get();
        }

        return $platform->id;
    }

    protected function syncPivotTablesUsingRawQueries($pivotDataBatch, $jsonDataBatch)
    {
        // Retrieve the domains that were just inserted or updated
        $domainNames = array_column($pivotDataBatch, 'name');
        $domains = Domain::on('mysql::write')->whereIn('name', $domainNames)->select(['id','name'])->get();

        $categoryPivotData = [];
        $appPivotData = [];
        $technologyPivotData = [];
        $contactTypePivotData = [];
        $featurePivotData = [];
        $tagPivotData = [];
        $salesChannelPivotData = [];
        $shippingCountryPivotData = [];
        $shippingCarrierPivotData = [];
        $domainDataPivotData = [];
        $jsonData = collect($jsonDataBatch);

        foreach ($pivotDataBatch as $pivotData) {
            $domain = $domains->firstWhere('name', $pivotData['name']);

            if ($domain) {
                if ($pivotData['update']) {
                    // Delete old entries from the pivot tables for this domain
                    DB::table('sl_category_domain')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_app_domain')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_domain_technology')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_contact_type_domain')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_domain_feature')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_domain_tag')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_domain_sales_channel')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_domain_shipping_country')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_domain_shipping_carrier')->where('domain_id', $domain->id)->delete();
                    DB::table('sl_domain_data')->where('id', $domain->id)->delete();
                }

                // Prepare new entries for the pivot tables for this domain
                if (!empty($pivotData['categories'])) {
                    foreach ($pivotData['categories'] as $categoryId) {
                        $categoryPivotData[] = [
                            'domain_id' => $domain->id,
                            'category_id' => $categoryId,
                        ];
                    }
                }

                if (!empty($pivotData['apps'])) {
                    foreach ($pivotData['apps'] as $appId) {
                        $appPivotData[] = [
                            'domain_id' => $domain->id,
                            'app_id' => $appId,
                        ];
                    }
                }

                if (!empty($pivotData['technologies'])) {
                    foreach ($pivotData['technologies'] as $technologyId) {
                        $technologyPivotData[] = [
                            'domain_id' => $domain->id,
                            'technology_id' => $technologyId,
                        ];
                    }
                }

                if (!empty($pivotData['contactTypes'])) {
                    foreach ($pivotData['contactTypes'] as $contactTypeId) {
                        $contactTypePivotData[] = [
                            'domain_id' => $domain->id,
                            'contact_type_id' => $contactTypeId,
                        ];
                    }
                }

                if (!empty($pivotData['features'])) {
                    foreach ($pivotData['features'] as $featureId) {
                        $featurePivotData[] = [
                            'domain_id' => $domain->id,
                            'feature_id' => $featureId,
                        ];
                    }
                }

                if (!empty($pivotData['tags'])) {
                    foreach ($pivotData['tags'] as $tagId) {
                        $tagPivotData[] = [
                            'domain_id' => $domain->id,
                            'tag_id' => $tagId,
                        ];
                    }
                }

                if (!empty($pivotData['salesChannels'])) {
                    foreach ($pivotData['salesChannels'] as $salesChannelId) {
                        $salesChannelPivotData[] = [
                            'domain_id' => $domain->id,
                            'sales_channel_id' => $salesChannelId,
                        ];
                    }
                }

                if (!empty($pivotData['shippingCountries'])) {
                    foreach ($pivotData['shippingCountries'] as $shippingCountryId) {
                        $shippingCountryPivotData[] = [
                            'domain_id' => $domain->id,
                            'shipping_country_id' => $shippingCountryId,
                        ];
                    }
                }

                if (!empty($pivotData['shippingCarriers'])) {
                    foreach ($pivotData['shippingCarriers'] as $shippingCarrierId) {
                        $shippingCarrierPivotData[] = [
                            'domain_id' => $domain->id,
                            'shipping_carrier_id' => $shippingCarrierId,
                        ];
                    }
                }

                // Find $jsonDataBatch for this domain and set the id from the domain
                $domainData = $jsonData->firstWhere('name', $pivotData['name']);
                if ($jsonData) {
                    $domainDataPivotData[] = [
                        'id' => $domain->id,
                        'data' => $domainData['data']
                    ];
                }
            }
        }

        // Insert new entries into the pivot tables in batch
        DB::table('sl_category_domain')->insert($categoryPivotData);
        DB::table('sl_app_domain')->insert($appPivotData);
        DB::table('sl_domain_technology')->insert($technologyPivotData);
        DB::table('sl_contact_type_domain')->insert($contactTypePivotData);
        DB::table('sl_domain_feature')->insert($featurePivotData);
        DB::table('sl_domain_tag')->insert($tagPivotData);
        DB::table('sl_domain_sales_channel')->insert($salesChannelPivotData);
        DB::table('sl_domain_shipping_country')->insert($shippingCountryPivotData);
        DB::table('sl_domain_shipping_carrier')->insert($shippingCarrierPivotData);
        DB::table('sl_domain_data')->insert($domainDataPivotData);
    }

    protected function checkMemory()
    {
        $currentMemoryUsage = memory_get_usage();
        $currentMemoryUsageMb = number_format($currentMemoryUsage / 1024 / 1024, 2, '.', '');
        $memoryLimit = ini_get('memory_limit');

        // Log the current memory usage and limit
        $this->logInfo("Current memory usage: {$currentMemoryUsageMb} MB / PHP memory limit: {$memoryLimit}");
    }

    protected function importBatch($domainsBatch, $pivotDataBatch, $jsonDataBatch)
    {
        DB::connection('mysql::write')->beginTransaction();
        try {
            Domain::upsert($domainsBatch, ['name'], [
                'week_id',
                'month_id',
                'platform_id',
//                'status_id',
                'employee_count_id',
                'estimated_sales_id',
//                'plan_id',
                'product_count_id',
                'region_id',
                'country_id',
                'currency_id',
                'language_id',
                'domain_type_id',
                'theme_vendor_id',
                'theme_id',
            ]);

            $this->syncPivotTablesUsingRawQueries($pivotDataBatch, $jsonDataBatch);

            DB::connection('mysql::write')->commit();

        } catch (\Throwable $e) {
            DB::connection('mysql::write')->rollBack();
            $this->logError('Failed to insert domains');
            throw $e;
        }
    }
}
