<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Campaign;
use App\Company;

class FetchWebsiteData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 2;
    private $campaign;
    private $company;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $company = Company::whereHas('prospect', function($q) use($emailTemplate) {
                $q->where('campaign_id', $this->campaign->id);
            })
            ->whereNull('title')
            ->first();

        if(!$company) {
            return true;
        }

        // var_dump($company->getAttributes());

        // WIP
    }
}
