<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class DailyCommandsMissingNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $missingCommands;

    /**
     * Create a new message instance.
     *
     * @param \Illuminate\Support\Collection $missingCommands
     */
    public function __construct($missingCommands)
    {
        $this->missingCommands = $missingCommands;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('admin.emails.daily-commands-missing-notification')
            ->subject('Daily Jobs Execution Warning');
    }
}
