<?php

namespace App\Models\StoreLeads;

use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Rennok<PERSON>\QueryCache\Traits\QueryCacheable;

class Domain extends Model
{
    use QueryCacheable;

//    public const PLATFORMS = [
//        '0' => 'unknown',
//        '1' => 'woocommerce',
//        '2' => 'shopify',
//    ];
//
//    public const STATUSES = [
//        '0' => 'unknown',
//        '1' => 'active',
//        '2' => 'password protected',
//    ];

    // Use this constant to set the cache time for queries using the cache (mainly frontend queries)
    public const CACHE_FOR = 60 * 60 * 24 * 90; // cache time, in seconds (90 days)

    public const EMPLOYEE_COUNTS = [
        '0' => [
            'name' => 'unknown',
            'min' => 0,
            'max' => 0,
        ],
        '1' => [
            'name' => '1 - 9',
            'min' => 1,
            'max' => 9,
        ],
        '2' => [
            'name' => '10 - 24',
            'min' => 10,
            'max' => 24,
        ],
        '3' => [
            'name' => '25 - 49',
            'min' => 25,
            'max' => 49,
        ],
        '4' => [
            'name' => '50 - 99',
            'min' => 50,
            'max' => 99,
        ],
        '5' => [
            'name' => '100 - 249',
            'min' => 100,
            'max' => 249,
        ],
        '6' => [
            'name' => '250 - 999',
            'min' => 250,
            'max' => 999,
        ],
        '7' => [
            'name' => '1000 - 4999',
            'min' => 1000,
            'max' => 4999,
        ],
        '8' => [
            'name' => '5000+',
            'min' => 5000,
            'max' => 999999999,
        ],
    ];

    public const ESTIMATED_SALES = [
        '0' => [
            'id' => 0,
            'name' => 'unknown',
            'min' => 0,
            'max' => 0,
        ],
        '1' => [
            'id' => 1,
            'name' => 'Under $50k/month',
            'min' => 1,
            'max' => 4999999,
        ],
        '2' => [
            'id' => 2,
            'name' => '$50k - 100k/month',
            'min' => 5000000,
            'max' => 9999999,
        ],
        '3' => [
            'id' => 3,
            'name' => '$100k - 250k/month',
            'min' => 10000000,
            'max' => 24999999,
        ],
        '4' => [
            'id' => 4,
            'name' => '$250k - 500k/month',
            'min' => 25000000,
            'max' => 49999999,
        ],
        '5' => [
            'id' => 5,
            'name' => '$500k - 1m/month',
            'min' => 50000000,
            'max' => 99999999,
        ],
        '6' => [
            'id' => 6,
            'name' => '$1m - 5m/month',
            'min' => 100000000,
            'max' => 499999999,
        ],
        '7' => [
            'id' => 7,
            'name' => '$5+m/month',
            'min' => 500000000,
            'max' => 9999999999999999,
        ],
    ];

//    public const PLANS = [
//        '0' => 'unknown',
//        '1' => 'shopify plus',
//        '2' => 'premium',
//        '3' => 'free',
//    ];

    public const PRODUCT_COUNTS = [
        '0' => [
            'id' => 0,
            'name' => 'unknown',
            'min' => 0,
            'max' => 0,
        ],
        '1' => [
            'id' => 1,
            'name' => '1 - 9',
            'min' => 1,
            'max' => 9,
        ],
        '2' => [
            'id' => 2,
            'name' => '10 - 24',
            'min' => 10,
            'max' => 24,
        ],
        '3' => [
            'id' => 3,
            'name' => '25 - 49',
            'min' => 25,
            'max' => 49,
        ],
        '4' => [
            'id' => 4,
            'name' => '50 - 99',
            'min' => 50,
            'max' => 99,
        ],
        '5' => [
            'id' => 5,
            'name' => '100 - 249',
            'min' => 100,
            'max' => 249,
        ],
        '6' => [
            'id' => 6,
            'name' => '250 - 999',
            'min' => 250,
            'max' => 999,
        ],
        '7' => [
            'id' => 7,
            'name' => '1000 - 4999',
            'min' => 1000,
            'max' => 4999,
        ],
        '8' => [
            'id' => 8,
            'name' => '5000 - 9999',
            'min' => 5000,
            'max' => 9999,
        ],
        '9' => [
            'id' => 9,
            'name' => '10000 - 24999',
            'min' => 10000,
            'max' => 24999,
        ],
        '10' => [
            'id' => 10,
            'name' => '25000 - 99999',
            'min' => 25000,
            'max' => 99999,
        ],
        '11' => [
            'id' => 11,
            'name' => '100000 - 249999',
            'min' => 100000,
            'max' => 249999,
        ],
        '12' => [
            'id' => 12,
            'name' => '250000+',
            'min' => 250000,
            'max' => 999999999,
        ],
    ];

    public const REGIONS = [
        '0' => 'unknown',
        '1' => 'americas',
        '2' => 'europe',
        '3' => 'asia',
        '4' => 'africa',
        '5' => 'oceania',
    ];

//    public const DOMAIN_TYPES = [
//        '0' => 'unknown',
//        '1' => 'custom',
//        '2' => 'platform domain',
//    ];

    // Query Cache config
//    public $cacheFor = 60 * 60 * 24 * 90; // cache time, in seconds (90 days)
    // No need for custom tag. The trait uses automatically generated tag: App\\Models\\StoreLeads\\Domain
//    public $cacheTags = ['sl_domains']; // Use this to flush the cache manually
    public $cacheDriver = 'redis_cache'; // Use this cache driver
//    protected static $flushCacheOnUpdate = true;

    protected $appends = [
        'employee_count',
        'estimated_sales',
        'product_count',
        'region',
//        'domain_type'
    ];

    protected $table = 'sl_domains';

    protected $guarded = [];

    protected $casts = [
        'last_searched_at' => 'datetime',
        'apollo_searched_at' => 'datetime',
    ];

    /*
     * Relationships
     */

    public function domainData()
    {
        return $this->hasOne(DomainData::class, 'id', 'id');
    }

    public function domainClusters()
    {
        return $this->hasMany(DomainCluster::class, 'domain_id');
    }

    public function month()
    {
        return $this->belongsTo(Month::class, 'month_id');
    }

    public function week()
    {
        return $this->belongsTo(Week::class, 'week_id');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'sl_category_domain', 'domain_id', 'category_id');
    }

    public function apps()
    {
        return $this->belongsToMany(App::class, 'sl_app_domain', 'domain_id', 'app_id');
    }

    public function technologies()
    {
        return $this->belongsToMany(Technology::class, 'sl_domain_technology', 'domain_id', 'technology_id');
    }

    public function contactTypes()
    {
        return $this->belongsToMany(ContactType::class, 'sl_contact_type_domain', 'domain_id', 'contact_type_id');
    }

    public function phoneCountryCodes()
    {
        return $this->belongsToMany(PhoneCountryCode::class, 'sl_domain_phone_country_code', 'domain_id', 'phone_country_code_id');
    }

    public function features()
    {
        return $this->belongsToMany(Feature::class, 'sl_domain_feature', 'domain_id', 'feature_id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id');
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'sl_domain_tag', 'domain_id', 'tag_id');
    }

    public function language()
    {
        return $this->belongsTo(Language::class, 'language_id');
    }

    public function topLevelDomain()
    {
        return $this->belongsTo(TopLevelDomain::class, 'top_level_domain_id');
    }

    public function salesChannels()
    {
        return $this->belongsToMany(SalesChannel::class, 'sl_domain_sales_channel', 'domain_id', 'sales_channel_id');
    }

    public function shippingCountries()
    {
        return $this->belongsToMany(ShippingCountry::class, 'sl_domain_shipping_country', 'domain_id', 'shipping_country_id');
    }

    public function shippingCarriers()
    {
        return $this->belongsToMany(ShippingCarrier::class, 'sl_domain_shipping_carrier', 'domain_id', 'shipping_carrier_id');
    }

    public function themeVendor()
    {
        return $this->belongsTo(ThemeVendor::class, 'theme_vendor_id');
    }

    public function theme()
    {
        return $this->belongsTo(Theme::class, 'theme_id');
    }

    public function platform()
    {
        return $this->belongsTo(Platform::class, 'platform_id');
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class, 'plan_id');
    }

    public function searchedDomain()
    {
        return $this->hasOne(SearchedDomain::class, 'id', 'id');
    }

    public function agencyDomains()
    {
        return $this->hasMany(AgencyDomain::class, 'domain_id');
    }

    public function agencyDomain()
    {
        return $this->hasOne(AgencyDomain::class, 'domain_id')
            ->whereNotNull('current_email_id')
            ->latestOfMany();
    }

    /*
     * Query Scopes
     */

    // Add a category field to domain query results showing the 1st category
    public function scopeWithFirstCategory($query, $categoryIds = [])
    {
        return $query->addSelect([
            'category' => DB::table('sl_categories')
                ->select('name')
                ->join('sl_category_domain', 'sl_categories.id', '=', 'sl_category_domain.category_id')
                ->whereColumn('sl_category_domain.domain_id', 'sl_domains.id')
                ->when(count($categoryIds), function (Builder $query) use ($categoryIds) {
                    $query->whereIn('sl_categories.id', $categoryIds);
                })
                ->limit(1)
        ]);
    }

    // Add a country field to domain query results showing the country code
    public function scopeWithCountry($query, $countryIds)
    {
        return $query->addSelect([
            'country' => DB::table('sl_countries')
                ->select('code')
                ->whereColumn('sl_countries.id', '=', 'sl_domains.country_id')
                ->when(count($countryIds), function (Builder $query) use ($countryIds) {
                    $query->whereIn('sl_countries.id', $countryIds);
                })
                ->limit(1)
        ]);
    }

    // Add a platform field to domain query results showing the country code
    public function scopeWithPlatform($query, $platformIds)
    {
        return $query->addSelect([
            'platform' => DB::table('sl_platforms')
                ->select('name')
                ->whereColumn('sl_platforms.id', '=', 'sl_domains.platform_id')
                ->when(count($platformIds), function (Builder $query) use ($platformIds) {
                    $query->whereIn('sl_platforms.id', $platformIds);
                })
                ->limit(1)
        ]);
    }

    public function scopeNotOnAgency($query, $agencyId)
    {
        if (!empty($agencyId)) {
            return $query->whereNotExists(function (Builder $query) use ($agencyId) {
                $query->select(DB::raw(1))
                    ->from('sl_agency_domains')
                    ->whereColumn('sl_agency_domains.domain_id', '=', 'sl_domains.id')
                    ->where('sl_agency_domains.agency_id', $agencyId);
            });
        }

        return $query;
    }

    public function scopeNotFailedOnAgency($query, $agencyId)
    {
        if (!empty($agencyId)) {
            return $query->whereNotExists(function (Builder $query) use ($agencyId) {
                $query->select(DB::raw(1))
                    ->from('sl_agency_domains')
                    ->whereColumn('sl_agency_domains.domain_id', '=', 'sl_domains.id')
                    ->where('sl_agency_domains.agency_id', $agencyId)
                    ->where('sl_agency_domains.email_search_status', 'failed');
            });
        }

        return $query;
    }

    public function scopeNotOnSuppression($query, $teamIds, $excludeSuppression)
    {
        if(!empty($teamIds) && $excludeSuppression == 'yes') {
            return $query->whereNotExists(function (Builder $query) use ($teamIds) {
                $query->select(DB::raw(1))
                    ->from('email_blocks')
                    ->whereColumn('email_blocks.domain_id', '=', 'sl_domains.id')
//                    ->where('email_blocks.type', 'domain')
                    ->whereIn('email_blocks.team_id', $teamIds);
            });
        }
        return $query;
    }

    public function scopeOfApps($query, $appIds)
    {
        if (count($appIds)) {
            return $query->whereExists(function (Builder $query) use ($appIds) {
                $query->select(DB::raw(1))
                    ->from('sl_app_domain')
                    ->whereColumn('sl_app_domain.domain_id', '=', 'sl_domains.id')
                    ->whereIn('sl_app_domain.app_id', $appIds);
            });
        }

        return $query;
    }

    /**
     * Filter domains by categories, including all descendant categories
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $categoryIds
     * @param bool $includeDescendants Whether to include descendant categories (default: true)
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfCategories($query, array $categoryIds, bool $includeDescendants = true)
    {
        if (count($categoryIds)) {
            $allCategoryIds = $categoryIds;

            if ($includeDescendants) {
                // Get all descendant categories (will use QueryCacheable automatically)
                $allCategoryIds = Category::expandCategoryIdsWithDescendants($categoryIds);
            }

            sort($allCategoryIds); // Ensure consistent order

            return $query->whereExists(function (Builder $query) use ($allCategoryIds) {
                $query->select(DB::raw(1))
                    ->from('sl_category_domain')
                    ->whereColumn('sl_category_domain.domain_id', '=', 'sl_domains.id')
                    ->whereIn('sl_category_domain.category_id', $allCategoryIds);
            });
        }

        return $query;
    }

    public function scopeOfCurrencies($query, $currencyIds)
    {
        if (count($currencyIds)) {
            return $query->whereIn('currency_id', $currencyIds);
        }

        return $query;
    }

    public function scopeOfCountries($query, $countryIds)
    {
        if (count($countryIds)) {
            return $query->whereIn('country_id', $countryIds);
        }

        return $query;
    }

    public function scopeOfContactTypes($query, $contactTypeIds)
    {
        if (count($contactTypeIds)) {
            return $query->whereExists(function (Builder $query) use ($contactTypeIds) {
                $query->select(DB::raw(1))
                    ->from('sl_contact_type_domain')
                    ->whereColumn('sl_contact_type_domain.domain_id', '=', 'sl_domains.id')
                    ->whereIn('sl_contact_type_domain.contact_type_id', $contactTypeIds);
            });
        }

        return $query;
    }

    public function scopeOfFeatures($query, $featureIds)
    {
        if (count($featureIds)) {
            return $query->whereExists(function (Builder $query) use ($featureIds) {
                $query->select(DB::raw(1))
                    ->from('sl_domain_feature')
                    ->whereColumn('sl_domain_feature.domain_id', '=', 'sl_domains.id')
                    ->whereIn('sl_domain_feature.feature_id', $featureIds);
            });
        }

        return $query;
    }

    public function scopeOfLanguages($query, $languageIds)
    {
        if (count($languageIds)) {
            return $query->whereIn('language_id', $languageIds);
        }

        return $query;
    }

    public function scopeOfMonths($query, $monthIds)
    {
        if (count($monthIds)) {
            return $query->whereIn('month_id', $monthIds);
        }

        return $query;
    }

    public function scopeOfProductCounts($query, $productIds)
    {
        if (count($productIds)) {
            return $query->whereIn('product_count_id', $productIds);
        }

        return $query;
    }

    public function scopeOfPlatforms($query, $platformIds)
    {
        if (count($platformIds)) {
            return $query->whereIn('platform_id', $platformIds);
        }

        return $query;
    }

    public function scopeOfPlans($query, $planIds)
    {
        if (count($planIds)) {
            return $query->whereIn('plan_id', $planIds);
        }

        return $query;
    }

    public function scopeOfSales($query, $saleIds)
    {
        if (count($saleIds)) {
            return $query->whereIn('estimated_sales_id', $saleIds);
        }

        return $query;
    }

    public function scopeOfSalesChannels($query, $salesChannelIds)
    {
        if (count($salesChannelIds)) {
            return $query->whereExists(function (Builder $query) use ($salesChannelIds) {
                $query->select(DB::raw(1))
                    ->from('sl_domain_sales_channel')
                    ->whereColumn('sl_domain_sales_channel.domain_id', '=', 'sl_domains.id')
                    ->whereIn('sl_domain_sales_channel.sales_channel_id', $salesChannelIds);
            });
        }

        return $query;
    }

    public function scopeOfShippingCountries($query, $shippingCountryIds)
    {
        if (count($shippingCountryIds)) {
            return $query->whereExists(function (Builder $query) use ($shippingCountryIds) {
                $query->where(DB::raw(1))
                    ->from('sl_domain_shipping_country')
                    ->whereColumn('sl_domain_shipping_country.domain_id', '=', 'sl_domains.id')
                    ->whereIn('sl_domain_shipping_country.shipping_country_id', $shippingCountryIds);
            });
        }

        return $query;
    }

    public function scopeOfShippingCarriers($query, $shippingCarrierIds)
    {
        if (count($shippingCarrierIds)) {
            return $query->whereExists(function (Builder $query) use ($shippingCarrierIds) {
                $query->where(DB::raw(1))
                    ->from('sl_domain_shipping_carrier')
                    ->whereColumn('sl_domain_shipping_carrier.domain_id' ,'=', 'sl_domains.id')
                    ->whereIn('sl_domain_shipping_carrier.shipping_carrier_id', $shippingCarrierIds);
            });
        }

        return $query;
    }

    public function scopeOfTags($query, $tagIds)
    {
        if (count($tagIds)) {
            return $query->whereExists(function (Builder $query) use ($tagIds) {
                $query->select(DB::raw(1))
                    ->from('sl_domain_tag')
                    ->whereColumn('sl_domain_tag.domain_id', '=', 'sl_domains.id')
                    ->whereIn('sl_domain_tag.tag_id', $tagIds);
            });
        }

        return $query;
    }

    public function scopeOfTechnologies($query, $technologyIds)
    {
        if (count($technologyIds)) {
            return $query->whereExists(function (Builder $query) use ($technologyIds) {
                $query->select(DB::raw(1))
                    ->from('sl_domain_technology')
                    ->whereColumn('sl_domain_technology.domain_id', '=', 'sl_domains.id')
                    ->whereIn('sl_domain_technology.technology_id', $technologyIds);
            });
        }

        return $query;
    }

    public function scopeOfThemes($query, $themeIds)
    {
        if (count($themeIds)) {
            return $query->whereIn('theme_id', $themeIds);
        }

        return $query;
    }

    public function scopeOfThemeVendors($query, $themeVendorIds)
    {
        if (count($themeVendorIds)) {
            return $query->whereIn('theme_vendor_id', $themeVendorIds);
        }

        return $query;
    }

    public function scopeOfKeywords($query, $keywords)
    {
        if ($keywords) {
            return $query->where('name', 'like', '%'.$keywords.'%')
                ->orWhere('merchant_name', 'like', '%'.$keywords.'%');
        }

        return $query;
    }

    public function scopeFilter(EloquentBuilder $query, array $filters, ?string $viewType = null, ?int $agencyId = null, ?array $teamIds = []): EloquentBuilder
    {
        // Special rule: if plans are set, we ignore platforms
        $platformIds = !empty($filters['planIds']) ? [] : ($filters['platformIds'] ?? []);

        return $query
            ->ofKeywords($filters['keywords'] ?? null)
            ->ofApps($filters['appIds'] ?? [])
            ->ofCategories($filters['categoryIds'] ?? [], true)
            ->ofCountries($filters['countryIds'] ?? [])
            ->ofContactTypes($filters['contactTypeIds'] ?? [])
            ->ofCurrencies($filters['currencyIds'] ?? [])
            ->ofFeatures($filters['featureIds'] ?? [])
            ->ofLanguages($filters['languageIds'] ?? [])
            ->ofMonths($filters['monthIds'] ?? [])
            ->ofProductCounts($filters['productCountIds'] ?? [])
            ->ofPlatforms($platformIds)
            ->ofPlans($filters['planIds'] ?? [])
            ->ofSales($filters['saleIds'] ?? [])
            ->ofSalesChannels($filters['salesChannelIds'] ?? [])
            ->ofShippingCarriers($filters['shippingCarrierIds'] ?? [])
            ->ofShippingCountries($filters['shippingCountryIds'] ?? [])
            ->ofTags($filters['tagIds'] ?? [])
            ->ofTechnologies($filters['technologyIds'] ?? [])
            ->when($viewType === 'new' && $agencyId, function ($q) use ($agencyId, $teamIds, $filters) {
                $q->notOnAgency($agencyId)
                    ->notOnSuppression($teamIds, $filters['excludeSuppression']);
            })
            ->where('id', '>', 0)
            ->where('failed_searches', 0);
    }

    /*
     * Mutators
     */

    public function getEmployeeCountAttribute()
    {
        return self::EMPLOYEE_COUNTS[$this->employee_count_id];
    }

    public function getEstimatedSalesAttribute()
    {
        return self::ESTIMATED_SALES[$this->estimated_sales_id];
    }

//    public function getPlanAttribute()
//    {
//        return self::PLANS[$this->plan_id];
//    }

    public function getProductCountAttribute()
    {
        return self::PRODUCT_COUNTS[$this->product_count_id];
    }

    public function getRegionAttribute()
    {
        return ucfirst(self::REGIONS[$this->region_id]);
    }

//    public function getDomainTypeAttribute()
//    {
//        return self::DOMAIN_TYPES[$this->domain_type_id];
//    }
}
