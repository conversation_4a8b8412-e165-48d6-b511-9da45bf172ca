<?php

namespace App\Models\StoreLeads;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlatformHost extends Model
{
    protected $table = 'sl_platform_hosts';

    protected $guarded = [];

    protected $fillable = [
        'registrable',
        'count_subdomains',
        'confidence',
        'is_platform',
        'created_at',
        'updated_at'
    ];

    public function getRelatedDomains()
    {
        return Domain::select('id','name','merchant_name')
    }


}
