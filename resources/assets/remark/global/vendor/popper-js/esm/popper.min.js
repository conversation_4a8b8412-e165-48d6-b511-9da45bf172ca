/*
 Copyright (C) <PERSON> 2017
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */for(var isBrowser='undefined'!=typeof window&&'undefined'!=typeof window.document,longerTimeoutBrowsers=['Edge','Trident','Firefox'],timeoutDuration=0,i=0;i<longerTimeoutBrowsers.length;i+=1)if(isBrowser&&0<=navigator.userAgent.indexOf(longerTimeoutBrowsers[i])){timeoutDuration=1;break}function microtaskDebounce(e){var t=!1;return function(){t||(t=!0,Promise.resolve().then(function(){t=!1,e()}))}}function taskDebounce(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},timeoutDuration))}}var supportsMicroTasks=isBrowser&&window.Promise,debounce=supportsMicroTasks?microtaskDebounce:taskDebounce;function isFunction(e){return e&&'[object Function]'==={}.toString.call(e)}function getStyleComputedProperty(e,t){if(1!==e.nodeType)return[];var o=window.getComputedStyle(e,null);return t?o[t]:o}function getParentNode(e){return'HTML'===e.nodeName?e:e.parentNode||e.host}function getScrollParent(e){if(!e)return window.document.body;switch(e.nodeName){case'HTML':case'BODY':return e.ownerDocument.body;case'#document':return e.body;}var t=getStyleComputedProperty(e),o=t.overflow,i=t.overflowX,n=t.overflowY;return /(auto|scroll)/.test(o+n+i)?e:getScrollParent(getParentNode(e))}function getOffsetParent(e){var t=e&&e.offsetParent,o=t&&t.nodeName;return o&&'BODY'!==o&&'HTML'!==o?-1!==['TD','TABLE'].indexOf(t.nodeName)&&'static'===getStyleComputedProperty(t,'position')?getOffsetParent(t):t:e?e.ownerDocument.documentElement:window.document.documentElement}function isOffsetContainer(e){var t=e.nodeName;return'BODY'!==t&&('HTML'===t||getOffsetParent(e.firstElementChild)===e)}function getRoot(e){return null===e.parentNode?e:getRoot(e.parentNode)}function findCommonOffsetParent(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return window.document.documentElement;var o=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,i=o?e:t,n=o?t:e,r=document.createRange();r.setStart(i,0),r.setEnd(n,0);var p=r.commonAncestorContainer;if(e!==p&&t!==p||i.contains(n))return isOffsetContainer(p)?p:getOffsetParent(p);var s=getRoot(e);return s.host?findCommonOffsetParent(s.host,t):findCommonOffsetParent(e,getRoot(t).host)}function getScroll(e){var t=1<arguments.length&&arguments[1]!==void 0?arguments[1]:'top',o='top'===t?'scrollTop':'scrollLeft',i=e.nodeName;if('BODY'===i||'HTML'===i){var n=e.ownerDocument.documentElement,r=e.ownerDocument.scrollingElement||n;return r[o]}return e[o]}function includeScroll(e,t){var o=2<arguments.length&&void 0!==arguments[2]&&arguments[2],i=getScroll(t,'top'),n=getScroll(t,'left'),r=o?-1:1;return e.top+=i*r,e.bottom+=i*r,e.left+=n*r,e.right+=n*r,e}function getBordersSize(e,t){var o='x'===t?'Left':'Top',i='Left'==o?'Right':'Bottom';return+e['border'+o+'Width'].split('px')[0]+ +e['border'+i+'Width'].split('px')[0]}var isIE10,isIE10$1=function(){return void 0==isIE10&&(isIE10=-1!==navigator.appVersion.indexOf('MSIE 10')),isIE10};function getSize(e,t,o,i){return Math.max(t['offset'+e],t['scroll'+e],o['client'+e],o['offset'+e],o['scroll'+e],isIE10$1()?o['offset'+e]+i['margin'+('Height'===e?'Top':'Left')]+i['margin'+('Height'===e?'Bottom':'Right')]:0)}function getWindowSizes(){var e=window.document.body,t=window.document.documentElement,o=isIE10$1()&&window.getComputedStyle(t);return{height:getSize('Height',e,t,o),width:getSize('Width',e,t,o)}}var classCallCheck=function(e,t){if(!(e instanceof t))throw new TypeError('Cannot call a class as a function')},createClass=function(){function e(e,t){for(var o,n=0;n<t.length;n++)o=t[n],o.enumerable=o.enumerable||!1,o.configurable=!0,'value'in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}return function(t,o,i){return o&&e(t.prototype,o),i&&e(t,i),t}}(),defineProperty=function(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e},_extends=Object.assign||function(e){for(var t,o=1;o<arguments.length;o++)for(var i in t=arguments[o],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e};function getClientRect(e){return _extends({},e,{right:e.left+e.width,bottom:e.top+e.height})}function getBoundingClientRect(e){var t={};if(isIE10$1())try{t=e.getBoundingClientRect();var o=getScroll(e,'top'),i=getScroll(e,'left');t.top+=o,t.left+=i,t.bottom+=o,t.right+=i}catch(e){}else t=e.getBoundingClientRect();var n={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},r='HTML'===e.nodeName?getWindowSizes():{},p=r.width||e.clientWidth||n.right-n.left,s=r.height||e.clientHeight||n.bottom-n.top,d=e.offsetWidth-p,a=e.offsetHeight-s;if(d||a){var l=getStyleComputedProperty(e);d-=getBordersSize(l,'x'),a-=getBordersSize(l,'y'),n.width-=d,n.height-=a}return getClientRect(n)}function getOffsetRectRelativeToArbitraryNode(e,t){var o=isIE10$1(),i='HTML'===t.nodeName,n=getBoundingClientRect(e),r=getBoundingClientRect(t),p=getScrollParent(e),s=getStyleComputedProperty(t),d=+s.borderTopWidth.split('px')[0],a=+s.borderLeftWidth.split('px')[0],l=getClientRect({top:n.top-r.top-d,left:n.left-r.left-a,width:n.width,height:n.height});if(l.marginTop=0,l.marginLeft=0,!o&&i){var f=+s.marginTop.split('px')[0],m=+s.marginLeft.split('px')[0];l.top-=d-f,l.bottom-=d-f,l.left-=a-m,l.right-=a-m,l.marginTop=f,l.marginLeft=m}return(o?t.contains(p):t===p&&'BODY'!==p.nodeName)&&(l=includeScroll(l,t)),l}function getViewportOffsetRectRelativeToArtbitraryNode(e){var t=Math.max,o=e.ownerDocument.documentElement,i=getOffsetRectRelativeToArbitraryNode(e,o),n=t(o.clientWidth,window.innerWidth||0),r=t(o.clientHeight,window.innerHeight||0),p=getScroll(o),s=getScroll(o,'left'),d={top:p-i.top+i.marginTop,left:s-i.left+i.marginLeft,width:n,height:r};return getClientRect(d)}function isFixed(e){var t=e.nodeName;return'BODY'===t||'HTML'===t?!1:!('fixed'!==getStyleComputedProperty(e,'position'))||isFixed(getParentNode(e))}function getBoundaries(e,t,o,i){var n={top:0,left:0},r=findCommonOffsetParent(e,t);if('viewport'===i)n=getViewportOffsetRectRelativeToArtbitraryNode(r);else{var p;'scrollParent'===i?(p=getScrollParent(getParentNode(e)),'BODY'===p.nodeName&&(p=e.ownerDocument.documentElement)):'window'===i?p=e.ownerDocument.documentElement:p=i;var s=getOffsetRectRelativeToArbitraryNode(p,r);if('HTML'===p.nodeName&&!isFixed(r)){var d=getWindowSizes(),a=d.height,l=d.width;n.top+=s.top-s.marginTop,n.bottom=a+s.top,n.left+=s.left-s.marginLeft,n.right=l+s.left}else n=s}return n.left+=o,n.top+=o,n.right-=o,n.bottom-=o,n}function getArea(e){var t=e.width,o=e.height;return t*o}function computeAutoPlacement(e,t,o,i,n){var r=5<arguments.length&&arguments[5]!==void 0?arguments[5]:0;if(-1===e.indexOf('auto'))return e;var p=getBoundaries(o,i,r,n),s={top:{width:p.width,height:t.top-p.top},right:{width:p.right-t.right,height:p.height},bottom:{width:p.width,height:p.bottom-t.bottom},left:{width:t.left-p.left,height:p.height}},d=Object.keys(s).map(function(e){return _extends({key:e},s[e],{area:getArea(s[e])})}).sort(function(e,t){return t.area-e.area}),a=d.filter(function(e){var t=e.width,i=e.height;return t>=o.clientWidth&&i>=o.clientHeight}),l=0<a.length?a[0].key:d[0].key,f=e.split('-')[1];return l+(f?'-'+f:'')}function getReferenceOffsets(e,t,o){var i=findCommonOffsetParent(t,o);return getOffsetRectRelativeToArbitraryNode(o,i)}function getOuterSizes(e){var t=window.getComputedStyle(e),o=parseFloat(t.marginTop)+parseFloat(t.marginBottom),i=parseFloat(t.marginLeft)+parseFloat(t.marginRight),n={width:e.offsetWidth+i,height:e.offsetHeight+o};return n}function getOppositePlacement(e){var t={left:'right',right:'left',bottom:'top',top:'bottom'};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function getPopperOffsets(e,t,o){o=o.split('-')[0];var i=getOuterSizes(e),n={width:i.width,height:i.height},r=-1!==['right','left'].indexOf(o),p=r?'top':'left',s=r?'left':'top',d=r?'height':'width',a=r?'width':'height';return n[p]=t[p]+t[d]/2-i[d]/2,n[s]=o===s?t[s]-i[a]:t[getOppositePlacement(s)],n}function find(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function findIndex(e,t,o){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===o});var i=find(e,function(e){return e[t]===o});return e.indexOf(i)}function runModifiers(e,t,o){var i=void 0===o?e:e.slice(0,findIndex(e,'name',o));return i.forEach(function(e){e['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');var o=e['function']||e.fn;e.enabled&&isFunction(o)&&(t.offsets.popper=getClientRect(t.offsets.popper),t.offsets.reference=getClientRect(t.offsets.reference),t=o(t,e))}),t}function update(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=getReferenceOffsets(this.state,this.popper,this.reference),e.placement=computeAutoPlacement(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.offsets.popper=getPopperOffsets(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position='absolute',e=runModifiers(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function isModifierEnabled(e,t){return e.some(function(e){var o=e.name,i=e.enabled;return i&&o===t})}function getSupportedPropertyName(e){for(var t=[!1,'ms','Webkit','Moz','O'],o=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<t.length-1;n++){var i=t[n],r=i?''+i+o:e;if('undefined'!=typeof window.document.body.style[r])return r}return null}function destroy(){return this.state.isDestroyed=!0,isModifierEnabled(this.modifiers,'applyStyle')&&(this.popper.removeAttribute('x-placement'),this.popper.style.left='',this.popper.style.position='',this.popper.style.top='',this.popper.style[getSupportedPropertyName('transform')]=''),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function getWindow(e){var t=e.ownerDocument;return t?t.defaultView:window}function attachToScrollParents(e,t,o,i){var n='BODY'===e.nodeName,r=n?e.ownerDocument.defaultView:e;r.addEventListener(t,o,{passive:!0}),n||attachToScrollParents(getScrollParent(r.parentNode),t,o,i),i.push(r)}function setupEventListeners(e,t,o,i){o.updateBound=i,getWindow(e).addEventListener('resize',o.updateBound,{passive:!0});var n=getScrollParent(e);return attachToScrollParents(n,'scroll',o.updateBound,o.scrollParents),o.scrollElement=n,o.eventsEnabled=!0,o}function enableEventListeners(){this.state.eventsEnabled||(this.state=setupEventListeners(this.reference,this.options,this.state,this.scheduleUpdate))}function removeEventListeners(e,t){return getWindow(e).removeEventListener('resize',t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener('scroll',t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function disableEventListeners(){this.state.eventsEnabled&&(window.cancelAnimationFrame(this.scheduleUpdate),this.state=removeEventListeners(this.reference,this.state))}function isNumeric(e){return''!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function setStyles(e,t){Object.keys(t).forEach(function(o){var i='';-1!==['width','height','top','right','bottom','left'].indexOf(o)&&isNumeric(t[o])&&(i='px'),e.style[o]=t[o]+i})}function setAttributes(e,t){Object.keys(t).forEach(function(o){var i=t[o];!1===i?e.removeAttribute(o):e.setAttribute(o,t[o])})}function applyStyle(e){return setStyles(e.instance.popper,e.styles),setAttributes(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&setStyles(e.arrowElement,e.arrowStyles),e}function applyStyleOnLoad(e,t,o,i,n){var r=getReferenceOffsets(n,t,e),p=computeAutoPlacement(o.placement,r,t,e,o.modifiers.flip.boundariesElement,o.modifiers.flip.padding);return t.setAttribute('x-placement',p),setStyles(t,{position:'absolute'}),o}function computeStyle(e,t){var o=Math.floor,i=t.x,n=t.y,r=e.offsets.popper,p=find(e.instance.modifiers,function(e){return'applyStyle'===e.name}).gpuAcceleration;void 0!==p&&console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');var s,d,a=void 0===p?t.gpuAcceleration:p,l=getOffsetParent(e.instance.popper),f=getBoundingClientRect(l),m={position:r.position},c={left:o(r.left),top:o(r.top),bottom:o(r.bottom),right:o(r.right)},h='bottom'===i?'top':'bottom',g='right'===n?'left':'right',u=getSupportedPropertyName('transform');if(d='bottom'==h?-f.height+c.bottom:c.top,s='right'==g?-f.width+c.right:c.left,a&&u)m[u]='translate3d('+s+'px, '+d+'px, 0)',m[h]=0,m[g]=0,m.willChange='transform';else{var b='bottom'==h?-1:1,y='right'==g?-1:1;m[h]=d*b,m[g]=s*y,m.willChange=h+', '+g}var w={"x-placement":e.placement};return e.attributes=_extends({},w,e.attributes),e.styles=_extends({},m,e.styles),e.arrowStyles=_extends({},e.offsets.arrow,e.arrowStyles),e}function isModifierRequired(e,t,o){var i=find(e,function(e){var o=e.name;return o===t}),n=!!i&&e.some(function(e){return e.name===o&&e.enabled&&e.order<i.order});if(!n){var r='`'+t+'`';console.warn('`'+o+'`'+' modifier is required by '+r+' modifier in order to work, be sure to include it before '+r+'!')}return n}function arrow(e,t){if(!isModifierRequired(e.instance.modifiers,'arrow','keepTogether'))return e;var o=t.element;if('string'==typeof o){if(o=e.instance.popper.querySelector(o),!o)return e;}else if(!e.instance.popper.contains(o))return console.warn('WARNING: `arrow.element` must be child of its popper element!'),e;var i=e.placement.split('-')[0],n=e.offsets,r=n.popper,p=n.reference,s=-1!==['left','right'].indexOf(i),d=s?'height':'width',a=s?'Top':'Left',l=a.toLowerCase(),f=s?'left':'top',m=s?'bottom':'right',c=getOuterSizes(o)[d];p[m]-c<r[l]&&(e.offsets.popper[l]-=r[l]-(p[m]-c)),p[l]+c>r[m]&&(e.offsets.popper[l]+=p[l]+c-r[m]);var h=p[l]+p[d]/2-c/2,g=getStyleComputedProperty(e.instance.popper,'margin'+a).replace('px',''),u=h-getClientRect(e.offsets.popper)[l]-g;return u=Math.max(Math.min(r[d]-c,u),0),e.arrowElement=o,e.offsets.arrow={},e.offsets.arrow[l]=Math.round(u),e.offsets.arrow[f]='',e}function getOppositeVariation(e){if('end'===e)return'start';return'start'===e?'end':e}var placements=['auto-start','auto','auto-end','top-start','top','top-end','right-start','right','right-end','bottom-end','bottom','bottom-start','left-end','left','left-start'],validPlacements=placements.slice(3);function clockwise(e){var t=1<arguments.length&&arguments[1]!==void 0&&arguments[1],o=validPlacements.indexOf(e),i=validPlacements.slice(o+1).concat(validPlacements.slice(0,o));return t?i.reverse():i}var BEHAVIORS={FLIP:'flip',CLOCKWISE:'clockwise',COUNTERCLOCKWISE:'counterclockwise'};function flip(e,t){if(isModifierEnabled(e.instance.modifiers,'inner'))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var o=getBoundaries(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement),i=e.placement.split('-')[0],n=getOppositePlacement(i),r=e.placement.split('-')[1]||'',p=[];switch(t.behavior){case BEHAVIORS.FLIP:p=[i,n];break;case BEHAVIORS.CLOCKWISE:p=clockwise(i);break;case BEHAVIORS.COUNTERCLOCKWISE:p=clockwise(i,!0);break;default:p=t.behavior;}return p.forEach(function(s,d){if(i!==s||p.length===d+1)return e;i=e.placement.split('-')[0],n=getOppositePlacement(i);var a=e.offsets.popper,l=e.offsets.reference,f=Math.floor,m='left'===i&&f(a.right)>f(l.left)||'right'===i&&f(a.left)<f(l.right)||'top'===i&&f(a.bottom)>f(l.top)||'bottom'===i&&f(a.top)<f(l.bottom),c=f(a.left)<f(o.left),h=f(a.right)>f(o.right),g=f(a.top)<f(o.top),u=f(a.bottom)>f(o.bottom),b='left'===i&&c||'right'===i&&h||'top'===i&&g||'bottom'===i&&u,y=-1!==['top','bottom'].indexOf(i),w=!!t.flipVariations&&(y&&'start'===r&&c||y&&'end'===r&&h||!y&&'start'===r&&g||!y&&'end'===r&&u);(m||b||w)&&(e.flipped=!0,(m||b)&&(i=p[d+1]),w&&(r=getOppositeVariation(r)),e.placement=i+(r?'-'+r:''),e.offsets.popper=_extends({},e.offsets.popper,getPopperOffsets(e.instance.popper,e.offsets.reference,e.placement)),e=runModifiers(e.instance.modifiers,e,'flip'))}),e}function keepTogether(e){var t=e.offsets,o=t.popper,i=t.reference,n=e.placement.split('-')[0],r=Math.floor,p=-1!==['top','bottom'].indexOf(n),s=p?'right':'bottom',d=p?'left':'top',a=p?'width':'height';return o[s]<r(i[d])&&(e.offsets.popper[d]=r(i[d])-o[a]),o[d]>r(i[s])&&(e.offsets.popper[d]=r(i[s])),e}function toValue(e,t,o,i){var n=Math.max,r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),p=+r[1],s=r[2];if(!p)return e;if(0===s.indexOf('%')){var d;switch(s){case'%p':d=o;break;case'%':case'%r':default:d=i;}var a=getClientRect(d);return a[t]/100*p}if('vh'===s||'vw'===s){var l;return l='vh'===s?n(document.documentElement.clientHeight,window.innerHeight||0):n(document.documentElement.clientWidth,window.innerWidth||0),l/100*p}return p}function parseOffset(e,t,o,i){var n=[0,0],r=-1!==['right','left'].indexOf(i),p=e.split(/(\+|\-)/).map(function(e){return e.trim()}),s=p.indexOf(find(p,function(e){return-1!==e.search(/,|\s/)}));p[s]&&-1===p[s].indexOf(',')&&console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');var d=/\s*,\s*|\s+/,a=-1===s?[p]:[p.slice(0,s).concat([p[s].split(d)[0]]),[p[s].split(d)[1]].concat(p.slice(s+1))];return a=a.map(function(e,i){var n=(1===i?!r:r)?'height':'width',p=!1;return e.reduce(function(e,t){return''===e[e.length-1]&&-1!==['+','-'].indexOf(t)?(e[e.length-1]=t,p=!0,e):p?(e[e.length-1]+=t,p=!1,e):e.concat(t)},[]).map(function(e){return toValue(e,n,t,o)})}),a.forEach(function(e,t){e.forEach(function(o,i){isNumeric(o)&&(n[t]+=o*('-'===e[i-1]?-1:1))})}),n}function offset(e,t){var o,i=t.offset,n=e.placement,r=e.offsets,p=r.popper,s=r.reference,d=n.split('-')[0];return o=isNumeric(+i)?[+i,0]:parseOffset(i,p,s,d),'left'===d?(p.top+=o[0],p.left-=o[1]):'right'===d?(p.top+=o[0],p.left+=o[1]):'top'===d?(p.left+=o[0],p.top-=o[1]):'bottom'===d&&(p.left+=o[0],p.top+=o[1]),e.popper=p,e}function preventOverflow(e,t){var o=t.boundariesElement||getOffsetParent(e.instance.popper);e.instance.reference===o&&(o=getOffsetParent(o));var i=getBoundaries(e.instance.popper,e.instance.reference,t.padding,o);t.boundaries=i;var n=t.priority,r=e.offsets.popper,p={primary:function(e){var o=r[e];return r[e]<i[e]&&!t.escapeWithReference&&(o=Math.max(r[e],i[e])),defineProperty({},e,o)},secondary:function(e){var o='right'===e?'left':'top',n=r[o];return r[e]>i[e]&&!t.escapeWithReference&&(n=Math.min(r[o],i[e]-('right'===e?r.width:r.height))),defineProperty({},o,n)}};return n.forEach(function(e){var t=-1===['left','top'].indexOf(e)?'secondary':'primary';r=_extends({},r,p[t](e))}),e.offsets.popper=r,e}function shift(e){var t=e.placement,o=t.split('-')[0],i=t.split('-')[1];if(i){var n=e.offsets,r=n.reference,p=n.popper,s=-1!==['bottom','top'].indexOf(o),d=s?'left':'top',a=s?'width':'height',l={start:defineProperty({},d,r[d]),end:defineProperty({},d,r[d]+r[a]-p[a])};e.offsets.popper=_extends({},p,l[i])}return e}function hide(e){if(!isModifierRequired(e.instance.modifiers,'hide','preventOverflow'))return e;var t=e.offsets.reference,o=find(e.instance.modifiers,function(e){return'preventOverflow'===e.name}).boundaries;if(t.bottom<o.top||t.left>o.right||t.top>o.bottom||t.right<o.left){if(!0===e.hide)return e;e.hide=!0,e.attributes['x-out-of-boundaries']=''}else{if(!1===e.hide)return e;e.hide=!1,e.attributes['x-out-of-boundaries']=!1}return e}function inner(e){var t=e.placement,o=t.split('-')[0],i=e.offsets,n=i.popper,r=i.reference,p=-1!==['left','right'].indexOf(o),s=-1===['top','left'].indexOf(o);return n[p?'left':'top']=r[o]-(s?n[p?'width':'height']:0),e.placement=getOppositePlacement(t),e.offsets.popper=getClientRect(n),e}var modifiers={shift:{order:100,enabled:!0,fn:shift},offset:{order:200,enabled:!0,fn:offset,offset:0},preventOverflow:{order:300,enabled:!0,fn:preventOverflow,priority:['left','right','top','bottom'],padding:5,boundariesElement:'scrollParent'},keepTogether:{order:400,enabled:!0,fn:keepTogether},arrow:{order:500,enabled:!0,fn:arrow,element:'[x-arrow]'},flip:{order:600,enabled:!0,fn:flip,behavior:'flip',padding:5,boundariesElement:'viewport'},inner:{order:700,enabled:!1,fn:inner},hide:{order:800,enabled:!0,fn:hide},computeStyle:{order:850,enabled:!0,fn:computeStyle,gpuAcceleration:!0,x:'bottom',y:'right'},applyStyle:{order:900,enabled:!0,fn:applyStyle,onLoad:applyStyleOnLoad,gpuAcceleration:void 0}},Defaults={placement:'bottom',eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:modifiers},Popper=function(){function e(t,o){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};classCallCheck(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=debounce(this.update.bind(this)),this.options=_extends({},e.Defaults,n),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=o&&o.jquery?o[0]:o,this.options.modifiers={},Object.keys(_extends({},e.Defaults.modifiers,n.modifiers)).forEach(function(t){i.options.modifiers[t]=_extends({},e.Defaults.modifiers[t]||{},n.modifiers?n.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return _extends({name:e},i.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&isFunction(e.onLoad)&&e.onLoad(i.reference,i.popper,i.options,e,i.state)}),this.update();var r=this.options.eventsEnabled;r&&this.enableEventListeners(),this.state.eventsEnabled=r}return createClass(e,[{key:'update',value:function(){return update.call(this)}},{key:'destroy',value:function(){return destroy.call(this)}},{key:'enableEventListeners',value:function(){return enableEventListeners.call(this)}},{key:'disableEventListeners',value:function(){return disableEventListeners.call(this)}}]),e}();Popper.Utils=('undefined'==typeof window?global:window).PopperUtils,Popper.placements=placements,Popper.Defaults=Defaults;export default Popper;
//# sourceMappingURL=popper.min.js.map
