
<!DOCTYPE HTML>
<html lang="" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>Installation · GitBook</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.2">
        <meta name="author" content="chartjs">
        
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
        <link rel="stylesheet" href="../style.css">
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="integration.html" />
    
    
    <link rel="prev" href="./" />
    

    <link rel="stylesheet" href="../gitbook/gitbook-plugin-chartjs/style.css">
    <script src="../gitbook/gitbook-plugin-chartjs/Chart.bundle.js"></script>
    <script src="../gitbook/gitbook-plugin-chartjs/chartjs-plugin-deferred.js"></script>
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="Type to search" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                    Chart.js
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="./">
            
                <a href="./">
            
                    
                    Getting Started
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter active" data-level="1.2.1" data-path="installation.html">
            
                <a href="installation.html">
            
                    
                    Installation
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="integration.html">
            
                <a href="integration.html">
            
                    
                    Integration
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="usage.html">
            
                <a href="usage.html">
            
                    
                    Usage
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="../general/">
            
                <a href="../general/">
            
                    
                    General
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="../general/responsive.html">
            
                <a href="../general/responsive.html">
            
                    
                    Responsive
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="../general/interactions/">
            
                <a href="../general/interactions/">
            
                    
                    Interactions
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="../general/interactions/events.html">
            
                <a href="../general/interactions/events.html">
            
                    
                    Events
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="../general/interactions/modes.html">
            
                <a href="../general/interactions/modes.html">
            
                    
                    Modes
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.3" data-path="../general/options.html">
            
                <a href="../general/options.html">
            
                    
                    Options
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.4" data-path="../general/colors.html">
            
                <a href="../general/colors.html">
            
                    
                    Colors
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.5" data-path="../general/fonts.html">
            
                <a href="../general/fonts.html">
            
                    
                    Fonts
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" data-path="../configuration/">
            
                <a href="../configuration/">
            
                    
                    Configuration
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="../configuration/animations.html">
            
                <a href="../configuration/animations.html">
            
                    
                    Animations
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="../configuration/layout.html">
            
                <a href="../configuration/layout.html">
            
                    
                    Layout
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.3" data-path="../configuration/legend.html">
            
                <a href="../configuration/legend.html">
            
                    
                    Legend
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.4" data-path="../configuration/title.html">
            
                <a href="../configuration/title.html">
            
                    
                    Title
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.5" data-path="../configuration/tooltip.html">
            
                <a href="../configuration/tooltip.html">
            
                    
                    Tooltip
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.6" data-path="../configuration/elements.html">
            
                <a href="../configuration/elements.html">
            
                    
                    Elements
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="../charts/">
            
                <a href="../charts/">
            
                    
                    Charts
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.5.1" data-path="../charts/line.html">
            
                <a href="../charts/line.html">
            
                    
                    Line
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.2" data-path="../charts/bar.html">
            
                <a href="../charts/bar.html">
            
                    
                    Bar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.3" data-path="../charts/radar.html">
            
                <a href="../charts/radar.html">
            
                    
                    Radar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.4" data-path="../charts/doughnut.html">
            
                <a href="../charts/doughnut.html">
            
                    
                    Doughnut & Pie
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.5" data-path="../charts/polar.html">
            
                <a href="../charts/polar.html">
            
                    
                    Polar Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.6" data-path="../charts/bubble.html">
            
                <a href="../charts/bubble.html">
            
                    
                    Bubble
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.7" data-path="../charts/scatter.html">
            
                <a href="../charts/scatter.html">
            
                    
                    Scatter
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.8" data-path="../charts/area.html">
            
                <a href="../charts/area.html">
            
                    
                    Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.9" data-path="../charts/mixed.html">
            
                <a href="../charts/mixed.html">
            
                    
                    Mixed
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6" data-path="../axes/">
            
                <a href="../axes/">
            
                    
                    Axes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="../axes/cartesian/">
            
                <a href="../axes/cartesian/">
            
                    
                    Cartesian
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1.1" data-path="../axes/cartesian/category.html">
            
                <a href="../axes/cartesian/category.html">
            
                    
                    Category
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.2" data-path="../axes/cartesian/linear.html">
            
                <a href="../axes/cartesian/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.3" data-path="../axes/cartesian/logarithmic.html">
            
                <a href="../axes/cartesian/logarithmic.html">
            
                    
                    Logarithmic
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.4" data-path="../axes/cartesian/time.html">
            
                <a href="../axes/cartesian/time.html">
            
                    
                    Time
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="../axes/radial/">
            
                <a href="../axes/radial/">
            
                    
                    Radial
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.2.1" data-path="../axes/radial/linear.html">
            
                <a href="../axes/radial/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="../axes/labelling.html">
            
                <a href="../axes/labelling.html">
            
                    
                    Labelling
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="../axes/styling.html">
            
                <a href="../axes/styling.html">
            
                    
                    Styling
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="../developers/">
            
                <a href="../developers/">
            
                    
                    Developers
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.7.1" data-path="../developers/api.html">
            
                <a href="../developers/api.html">
            
                    
                    Chart.js API
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="../developers/updates.html">
            
                <a href="../developers/updates.html">
            
                    
                    Updating Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="../developers/plugins.html">
            
                <a href="../developers/plugins.html">
            
                    
                    Plugins
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.4" data-path="../developers/charts.html">
            
                <a href="../developers/charts.html">
            
                    
                    New Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.5" data-path="../developers/axes.html">
            
                <a href="../developers/axes.html">
            
                    
                    New Axes
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.6" data-path="../developers/contributing.html">
            
                <a href="../developers/contributing.html">
            
                    
                    Contributing
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="../notes/">
            
                <a href="../notes/">
            
                    
                    Additional Notes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.8.1" data-path="../notes/comparison.html">
            
                <a href="../notes/comparison.html">
            
                    
                    Comparison Table
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.2" data-path="../notes/extensions.html">
            
                <a href="../notes/extensions.html">
            
                    
                    Popular Extensions
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.3" data-path="../notes/license.html">
            
                <a href="../notes/license.html">
            
                    
                    License
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            Published with GitBook
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >Installation</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="installation">Installation</h1>
<p>Chart.js can be installed via npm or bower. It is recommended to get Chart.js this way.</p>
<h2 id="npm">npm</h2>
<p><a href="https://npmjs.com/package/chart.js" target="_blank"><img src="https://img.shields.io/npm/v/chart.js.svg?style=flat-square&amp;maxAge=600" alt="npm"></a>
<a href="https://npmjs.com/package/chart.js" target="_blank"><img src="https://img.shields.io/npm/dm/chart.js.svg?style=flat-square&amp;maxAge=600" alt="npm"></a></p>
<pre><code class="lang-bash">npm install chart.js --save
</code></pre>
<h2 id="bower">Bower</h2>
<p><a href="https://libraries.io/bower/chartjs" target="_blank"><img src="https://img.shields.io/bower/v/chartjs.svg?style=flat-square&amp;maxAge=600" alt="bower"></a></p>
<pre><code class="lang-bash">bower install chart.js --save
</code></pre>
<h2 id="cdn">CDN</h2>
<h3 id="cdnjs">CDNJS</h3>
<p><a href="https://cdnjs.com/libraries/Chart.js" target="_blank"><img src="https://img.shields.io/cdnjs/v/Chart.js.svg?style=flat-square&amp;maxAge=600" alt="cdnjs"></a></p>
<p>Chart.js built files are available on <a href="https://cdnjs.com/" target="_blank">CDNJS</a>:</p>
<p><a href="https://cdnjs.com/libraries/Chart.js" target="_blank">https://cdnjs.com/libraries/Chart.js</a></p>
<h3 id="jsdelivr">jsDelivr</h3>
<p><a href="https://cdn.jsdelivr.net/npm/chart.js@latest/dist/" target="_blank"><img src="https://img.shields.io/npm/v/chart.js.svg?label=jsdelivr&amp;style=flat-square&amp;maxAge=600" alt="jsdelivr"></a> <a href="https://www.jsdelivr.com/package/npm/chart.js" target="_blank"><img src="https://data.jsdelivr.com/v1/package/npm/chart.js/badge" alt="jsdelivr hits"></a></p>
<p>Chart.js built files are also available through <a href="http://www.jsdelivr.com/" target="_blank">jsDelivr</a>:</p>
<p><a href="https://www.jsdelivr.com/package/npm/chart.js?path=dist" target="_blank">https://www.jsdelivr.com/package/npm/chart.js?path=dist</a></p>
<h2 id="github">Github</h2>
<p><a href="https://github.com/chartjs/Chart.js/releases/latest" target="_blank"><img src="https://img.shields.io/github/release/chartjs/Chart.js.svg?style=flat-square&amp;maxAge=600" alt="github"></a></p>
<p>You can download the latest version of <a href="https://github.com/chartjs/Chart.js/releases/latest" target="_blank">Chart.js on GitHub</a>.</p>
<p>If you download or clone the repository, you must <a href="../developers/contributing.html#building-chartjs">build</a> Chart.js to generate the dist files. Chart.js no longer comes with prebuilt release versions, so an alternative option to downloading the repo is <strong>strongly</strong> advised.</p>
<h1 id="selecting-the-correct-build">Selecting the Correct Build</h1>
<p>Chart.js provides two different builds that are available for your use.</p>
<h2 id="stand-alone-build">Stand-Alone Build</h2>
<p>Files:</p>
<ul>
<li><code>dist/Chart.js</code></li>
<li><code>dist/Chart.min.js</code></li>
</ul>
<p>This version only includes Chart.js. If this version is used and you require the use of the time axis, <a href="http://momentjs.com/" target="_blank">Moment.js</a> will need to be included before Chart.js.</p>
<h2 id="bundled-build">Bundled Build</h2>
<p>Files:</p>
<ul>
<li><code>dist/Chart.bundle.js</code></li>
<li><code>dist/Chart.bundle.min.js</code></li>
</ul>
<p>The bundled version includes Moment.js built into the same file. This version should be used if you wish to use time axes and want a single file to include. Do not use this build if your application already includes Moment.js. If you do, Moment.js will be included twice, increasing the page load time and potentially introducing version issues.</p>

                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="./" class="navigation navigation-prev " aria-label="Previous page: Getting Started">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="integration.html" class="navigation navigation-next " aria-label="Next page: Integration">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Installation","level":"1.2.1","depth":2,"next":{"title":"Integration","level":"1.2.2","depth":2,"path":"getting-started/integration.md","ref":"getting-started/integration.md","articles":[]},"previous":{"title":"Getting Started","level":"1.2","depth":1,"path":"getting-started/README.md","ref":"getting-started/README.md","articles":[{"title":"Installation","level":"1.2.1","depth":2,"path":"getting-started/installation.md","ref":"getting-started/installation.md","articles":[]},{"title":"Integration","level":"1.2.2","depth":2,"path":"getting-started/integration.md","ref":"getting-started/integration.md","articles":[]},{"title":"Usage","level":"1.2.3","depth":2,"path":"getting-started/usage.md","ref":"getting-started/usage.md","articles":[]}]},"dir":"ltr"},"config":{"plugins":["-lunr","-search","search-plus","anchorjs","chartjs","ga"],"root":"./docs","styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"anchorjs":{"icon":"#","placement":"left","visible":"always"},"ga":{"configuration":"auto","token":"UA-28909194-3"},"theme-default":{"styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"search-plus":{},"chartjs":{"defaults":null},"highlight":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"chartjs","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"gitbook":"3.2.2"},"file":{"path":"getting-started/installation.md","mtime":"2017-10-28T15:03:49.270Z","type":"markdown"},"gitbook":{"version":"3.2.2","time":"2017-10-28T15:09:53.587Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="https://cdnjs.cloudflare.com/ajax/libs/anchor-js/3.1.1/anchor.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-anchorjs/anchor-style.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-ga/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

