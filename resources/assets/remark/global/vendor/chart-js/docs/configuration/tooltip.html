
<!DOCTYPE HTML>
<html lang="" >
    <head>
        <meta charset="UTF-8">
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
        <title>Tooltip · GitBook</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.2">
        <meta name="author" content="chartjs">
        
        
    
    <link rel="stylesheet" href="../gitbook/style.css">

    
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-search-plus/search.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-highlight/website.css">
                
            
                
                <link rel="stylesheet" href="../gitbook/gitbook-plugin-fontsettings/website.css">
                
            
        

    

    
        
        <link rel="stylesheet" href="../style.css">
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    
    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="elements.html" />
    
    
    <link rel="prev" href="title.html" />
    

    <link rel="stylesheet" href="../gitbook/gitbook-plugin-chartjs/style.css">
    <script src="../gitbook/gitbook-plugin-chartjs/Chart.bundle.js"></script>
    <script src="../gitbook/gitbook-plugin-chartjs/chartjs-plugin-deferred.js"></script>
    

    </head>
    <body>
        
<div class="book">
    <div class="book-summary">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="Type to search" />
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter " data-level="1.1" data-path="../">
            
                <a href="../">
            
                    
                    Chart.js
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2" data-path="../getting-started/">
            
                <a href="../getting-started/">
            
                    
                    Getting Started
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.2.1" data-path="../getting-started/installation.html">
            
                <a href="../getting-started/installation.html">
            
                    
                    Installation
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.2" data-path="../getting-started/integration.html">
            
                <a href="../getting-started/integration.html">
            
                    
                    Integration
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.2.3" data-path="../getting-started/usage.html">
            
                <a href="../getting-started/usage.html">
            
                    
                    Usage
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3" data-path="../general/">
            
                <a href="../general/">
            
                    
                    General
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.1" data-path="../general/responsive.html">
            
                <a href="../general/responsive.html">
            
                    
                    Responsive
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2" data-path="../general/interactions/">
            
                <a href="../general/interactions/">
            
                    
                    Interactions
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.3.2.1" data-path="../general/interactions/events.html">
            
                <a href="../general/interactions/events.html">
            
                    
                    Events
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.2.2" data-path="../general/interactions/modes.html">
            
                <a href="../general/interactions/modes.html">
            
                    
                    Modes
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.3.3" data-path="../general/options.html">
            
                <a href="../general/options.html">
            
                    
                    Options
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.4" data-path="../general/colors.html">
            
                <a href="../general/colors.html">
            
                    
                    Colors
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.3.5" data-path="../general/fonts.html">
            
                <a href="../general/fonts.html">
            
                    
                    Fonts
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.4" data-path="./">
            
                <a href="./">
            
                    
                    Configuration
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.4.1" data-path="animations.html">
            
                <a href="animations.html">
            
                    
                    Animations
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.2" data-path="layout.html">
            
                <a href="layout.html">
            
                    
                    Layout
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.3" data-path="legend.html">
            
                <a href="legend.html">
            
                    
                    Legend
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.4" data-path="title.html">
            
                <a href="title.html">
            
                    
                    Title
            
                </a>
            

            
        </li>
    
        <li class="chapter active" data-level="1.4.5" data-path="tooltip.html">
            
                <a href="tooltip.html">
            
                    
                    Tooltip
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.4.6" data-path="elements.html">
            
                <a href="elements.html">
            
                    
                    Elements
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.5" data-path="../charts/">
            
                <a href="../charts/">
            
                    
                    Charts
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.5.1" data-path="../charts/line.html">
            
                <a href="../charts/line.html">
            
                    
                    Line
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.2" data-path="../charts/bar.html">
            
                <a href="../charts/bar.html">
            
                    
                    Bar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.3" data-path="../charts/radar.html">
            
                <a href="../charts/radar.html">
            
                    
                    Radar
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.4" data-path="../charts/doughnut.html">
            
                <a href="../charts/doughnut.html">
            
                    
                    Doughnut & Pie
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.5" data-path="../charts/polar.html">
            
                <a href="../charts/polar.html">
            
                    
                    Polar Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.6" data-path="../charts/bubble.html">
            
                <a href="../charts/bubble.html">
            
                    
                    Bubble
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.7" data-path="../charts/scatter.html">
            
                <a href="../charts/scatter.html">
            
                    
                    Scatter
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.8" data-path="../charts/area.html">
            
                <a href="../charts/area.html">
            
                    
                    Area
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.5.9" data-path="../charts/mixed.html">
            
                <a href="../charts/mixed.html">
            
                    
                    Mixed
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6" data-path="../axes/">
            
                <a href="../axes/">
            
                    
                    Axes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1" data-path="../axes/cartesian/">
            
                <a href="../axes/cartesian/">
            
                    
                    Cartesian
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.1.1" data-path="../axes/cartesian/category.html">
            
                <a href="../axes/cartesian/category.html">
            
                    
                    Category
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.2" data-path="../axes/cartesian/linear.html">
            
                <a href="../axes/cartesian/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.3" data-path="../axes/cartesian/logarithmic.html">
            
                <a href="../axes/cartesian/logarithmic.html">
            
                    
                    Logarithmic
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.1.4" data-path="../axes/cartesian/time.html">
            
                <a href="../axes/cartesian/time.html">
            
                    
                    Time
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.2" data-path="../axes/radial/">
            
                <a href="../axes/radial/">
            
                    
                    Radial
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.6.2.1" data-path="../axes/radial/linear.html">
            
                <a href="../axes/radial/linear.html">
            
                    
                    Linear
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.6.3" data-path="../axes/labelling.html">
            
                <a href="../axes/labelling.html">
            
                    
                    Labelling
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.6.4" data-path="../axes/styling.html">
            
                <a href="../axes/styling.html">
            
                    
                    Styling
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.7" data-path="../developers/">
            
                <a href="../developers/">
            
                    
                    Developers
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.7.1" data-path="../developers/api.html">
            
                <a href="../developers/api.html">
            
                    
                    Chart.js API
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.2" data-path="../developers/updates.html">
            
                <a href="../developers/updates.html">
            
                    
                    Updating Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.3" data-path="../developers/plugins.html">
            
                <a href="../developers/plugins.html">
            
                    
                    Plugins
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.4" data-path="../developers/charts.html">
            
                <a href="../developers/charts.html">
            
                    
                    New Charts
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.5" data-path="../developers/axes.html">
            
                <a href="../developers/axes.html">
            
                    
                    New Axes
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.7.6" data-path="../developers/contributing.html">
            
                <a href="../developers/contributing.html">
            
                    
                    Contributing
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter " data-level="1.8" data-path="../notes/">
            
                <a href="../notes/">
            
                    
                    Additional Notes
            
                </a>
            

            
            <ul class="articles">
                
    
        <li class="chapter " data-level="1.8.1" data-path="../notes/comparison.html">
            
                <a href="../notes/comparison.html">
            
                    
                    Comparison Table
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.2" data-path="../notes/extensions.html">
            
                <a href="../notes/extensions.html">
            
                    
                    Popular Extensions
            
                </a>
            

            
        </li>
    
        <li class="chapter " data-level="1.8.3" data-path="../notes/license.html">
            
                <a href="../notes/license.html">
            
                    
                    License
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
            Published with GitBook
        </a>
    </li>
</ul>


                </nav>
            
        
    </div>

    <div class="book-body">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href=".." >Tooltip</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div class="search-plus" id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <h1 id="tooltips">Tooltips</h1>
<h2 id="tooltip-configuration">Tooltip Configuration</h2>
<p>The tooltip configuration is passed into the <code>options.tooltips</code> namespace. The global options for the chart tooltips is defined in <code>Chart.defaults.global.tooltips</code>.</p>
<table>
<thead>
<tr>
<th>Name</th>
<th>Type</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>enabled</code></td>
<td><code>Boolean</code></td>
<td><code>true</code></td>
<td>Are tooltips enabled</td>
</tr>
<tr>
<td><code>custom</code></td>
<td><code>Function</code></td>
<td><code>null</code></td>
<td>See <a href="#external-custom-tooltips">custom tooltip</a> section.</td>
</tr>
<tr>
<td><code>mode</code></td>
<td><code>String</code></td>
<td><code>&apos;nearest&apos;</code></td>
<td>Sets which elements appear in the tooltip. <a href="../general/interactions/modes.html#interaction-modes">more...</a>.</td>
</tr>
<tr>
<td><code>intersect</code></td>
<td><code>Boolean</code></td>
<td><code>true</code></td>
<td>if true, the tooltip mode applies only when the mouse position intersects with an element. If false, the mode will be applied at all times.</td>
</tr>
<tr>
<td><code>position</code></td>
<td><code>String</code></td>
<td><code>&apos;average&apos;</code></td>
<td>The mode for positioning the tooltip. <a href="#position-modes">more...</a></td>
</tr>
<tr>
<td><code>callbacks</code></td>
<td><code>Object</code></td>
<td></td>
<td>See the <a href="#tooltip-callbacks">callbacks section</a></td>
</tr>
<tr>
<td><code>itemSort</code></td>
<td><code>Function</code></td>
<td></td>
<td>Sort tooltip items. <a href="#sort-callback">more...</a></td>
</tr>
<tr>
<td><code>filter</code></td>
<td><code>Function</code></td>
<td></td>
<td>Filter tooltip items. <a href="#filter-callback">more...</a></td>
</tr>
<tr>
<td><code>backgroundColor</code></td>
<td><code>Color</code></td>
<td><code>&apos;rgba(0,0,0,0.8)&apos;</code></td>
<td>Background color of the tooltip.</td>
</tr>
<tr>
<td><code>titleFontFamily</code></td>
<td><code>String</code></td>
<td><code>&quot;&apos;Helvetica Neue&apos;, &apos;Helvetica&apos;, &apos;Arial&apos;, sans-serif&quot;</code></td>
<td>title font</td>
</tr>
<tr>
<td><code>titleFontSize</code></td>
<td><code>Number</code></td>
<td><code>12</code></td>
<td>Title font size</td>
</tr>
<tr>
<td><code>titleFontStyle</code></td>
<td><code>String</code></td>
<td><code>&apos;bold&apos;</code></td>
<td>Title font style</td>
</tr>
<tr>
<td><code>titleFontColor</code></td>
<td><code>Color</code></td>
<td><code>&apos;#fff&apos;</code></td>
<td>Title font color</td>
</tr>
<tr>
<td><code>titleSpacing</code></td>
<td><code>Number</code></td>
<td><code>2</code></td>
<td>Spacing to add to top and bottom of each title line.</td>
</tr>
<tr>
<td><code>titleMarginBottom</code></td>
<td><code>Number</code></td>
<td><code>6</code></td>
<td>Margin to add on bottom of title section.</td>
</tr>
<tr>
<td><code>bodyFontFamily</code></td>
<td><code>String</code></td>
<td><code>&quot;&apos;Helvetica Neue&apos;, &apos;Helvetica&apos;, &apos;Arial&apos;, sans-serif&quot;</code></td>
<td>body line font</td>
</tr>
<tr>
<td><code>bodyFontSize</code></td>
<td><code>Number</code></td>
<td><code>12</code></td>
<td>Body font size</td>
</tr>
<tr>
<td><code>bodyFontStyle</code></td>
<td><code>String</code></td>
<td><code>&apos;normal&apos;</code></td>
<td>Body font style</td>
</tr>
<tr>
<td><code>bodyFontColor</code></td>
<td><code>Color</code></td>
<td><code>&apos;#fff&apos;</code></td>
<td>Body font color</td>
</tr>
<tr>
<td><code>bodySpacing</code></td>
<td><code>Number</code></td>
<td><code>2</code></td>
<td>Spacing to add to top and bottom of each tooltip item.</td>
</tr>
<tr>
<td><code>footerFontFamily</code></td>
<td><code>String</code></td>
<td><code>&quot;&apos;Helvetica Neue&apos;, &apos;Helvetica&apos;, &apos;Arial&apos;, sans-serif&quot;</code></td>
<td>footer font</td>
</tr>
<tr>
<td><code>footerFontSize</code></td>
<td><code>Number</code></td>
<td><code>12</code></td>
<td>Footer font size</td>
</tr>
<tr>
<td><code>footerFontStyle</code></td>
<td><code>String</code></td>
<td><code>&apos;bold&apos;</code></td>
<td>Footer font style</td>
</tr>
<tr>
<td><code>footerFontColor</code></td>
<td><code>Color</code></td>
<td><code>&apos;#fff&apos;</code></td>
<td>Footer font color</td>
</tr>
<tr>
<td><code>footerSpacing</code></td>
<td><code>Number</code></td>
<td><code>2</code></td>
<td>Spacing to add to top and bottom of each fotter line.</td>
</tr>
<tr>
<td><code>footerMarginTop</code></td>
<td><code>Number</code></td>
<td><code>6</code></td>
<td>Margin to add before drawing the footer.</td>
</tr>
<tr>
<td><code>xPadding</code></td>
<td><code>Number</code></td>
<td><code>6</code></td>
<td>Padding to add on left and right of tooltip.</td>
</tr>
<tr>
<td><code>yPadding</code></td>
<td><code>Number</code></td>
<td><code>6</code></td>
<td>Padding to add on top and bottom of tooltip.</td>
</tr>
<tr>
<td><code>caretPadding</code></td>
<td><code>Number</code></td>
<td><code>2</code></td>
<td>Extra distance to move the end of the tooltip arrow away from the tooltip point.</td>
</tr>
<tr>
<td><code>caretSize</code></td>
<td><code>Number</code></td>
<td><code>5</code></td>
<td>Size, in px, of the tooltip arrow.</td>
</tr>
<tr>
<td><code>cornerRadius</code></td>
<td><code>Number</code></td>
<td><code>6</code></td>
<td>Radius of tooltip corner curves.</td>
</tr>
<tr>
<td><code>multiKeyBackground</code></td>
<td><code>Color</code></td>
<td><code>&apos;#fff&apos;</code></td>
<td>Color to draw behind the colored boxes when multiple items are in the tooltip</td>
</tr>
<tr>
<td><code>displayColors</code></td>
<td><code>Boolean</code></td>
<td><code>true</code></td>
<td>if true, color boxes are shown in the tooltip</td>
</tr>
<tr>
<td><code>borderColor</code></td>
<td><code>Color</code></td>
<td><code>&apos;rgba(0,0,0,0)&apos;</code></td>
<td>Color of the border</td>
</tr>
<tr>
<td><code>borderWidth</code></td>
<td><code>Number</code></td>
<td><code>0</code></td>
<td>Size of the border</td>
</tr>
</tbody>
</table>
<h3 id="position-modes">Position Modes</h3>
<p> Possible modes are:</p>
<ul>
<li>&apos;average&apos;</li>
<li>&apos;nearest&apos;</li>
</ul>
<p>&apos;average&apos; mode will place the tooltip at the average position of the items displayed in the tooltip. &apos;nearest&apos; will place the tooltip at the position of the element closest to the event position.</p>
<p>New modes can be defined by adding functions to the Chart.Tooltip.positioners map.</p>
<p>Example:</p>
<pre><code class="lang-javascript"><span class="hljs-comment">/**
 * Custom positioner
 * @function Chart.Tooltip.positioners.custom
 * @param elements {Chart.Element[]} the tooltip elements
 * @param eventPosition {Point} the position of the event in canvas coordinates
 * @returns {Point} the tooltip position
 */</span>
Chart.Tooltip.positioners.custom = <span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params">elements, eventPosition</span>) </span>{
    <span class="hljs-comment">/** @type {Chart.Tooltip} */</span>
    <span class="hljs-keyword">var</span> tooltip = <span class="hljs-keyword">this</span>;

    <span class="hljs-comment">/* ... */</span>

    <span class="hljs-keyword">return</span> {
        x: <span class="hljs-number">0</span>,
        y: <span class="hljs-number">0</span>
    };
}
</code></pre>
<h3 id="sort-callback">Sort Callback</h3>
<p>Allows sorting of <a href="#tooltip-item-interface">tooltip items</a>. Must implement at minimum a function that can be passed to <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort" target="_blank">Array.prototype.sort</a>.  This function can also accept a third parameter that is the data object passed to the chart.</p>
<h3 id="filter-callback">Filter Callback</h3>
<p>Allows filtering of <a href="#tooltip-item-interface">tooltip items</a>. Must implement at minimum a function that can be passed to <a href="https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Array/filter" target="_blank">Array.prototype.filter</a>. This function can also accept a second parameter that is the data object passed to the chart.</p>
<h2 id="tooltip-callbacks">Tooltip Callbacks</h2>
<p>The tooltip label configuration is nested below the tooltip configuration using the <code>callbacks</code> key. The tooltip has the following callbacks for providing text. For all functions, &apos;this&apos; will be the tooltip object created from the Chart.Tooltip constructor.</p>
<p>All functions are called with the same arguments: a <a href="#tooltip-item-interface">tooltip item</a> and the data object passed to the chart. All functions must return either a string or an array of strings. Arrays of strings are treated as multiple lines of text.</p>
<table>
<thead>
<tr>
<th>Name</th>
<th>Arguments</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>beforeTitle</code></td>
<td><code>Array[tooltipItem], data</code></td>
<td>Returns the text to render before the title.</td>
</tr>
<tr>
<td><code>title</code></td>
<td><code>Array[tooltipItem], data</code></td>
<td>Returns text to render as the title of the tooltip.</td>
</tr>
<tr>
<td><code>afterTitle</code></td>
<td><code>Array[tooltipItem], data</code></td>
<td>Returns text to render after the title.</td>
</tr>
<tr>
<td><code>beforeBody</code></td>
<td><code>Array[tooltipItem], data</code></td>
<td>Returns text to render before the body section.</td>
</tr>
<tr>
<td><code>beforeLabel</code></td>
<td><code>tooltipItem, data</code></td>
<td>Returns text to render before an individual label. This will be called for each item in the tooltip.</td>
</tr>
<tr>
<td><code>label</code></td>
<td><code>tooltipItem, data</code></td>
<td>Returns text to render for an individual item in the tooltip.</td>
</tr>
<tr>
<td><code>labelColor</code></td>
<td><code>tooltipItem, chart</code></td>
<td>Returns the colors to render for the tooltip item. <a href="#label-color-callback">more...</a></td>
</tr>
<tr>
<td><code>labelTextColor</code></td>
<td><code>tooltipItem, chart</code></td>
<td>Returns the colors for the text of the label for the tooltip item.</td>
</tr>
<tr>
<td><code>afterLabel</code></td>
<td><code>tooltipItem, data</code></td>
<td>Returns text to render after an individual label.</td>
</tr>
<tr>
<td><code>afterBody</code></td>
<td><code>Array[tooltipItem], data</code></td>
<td>Returns text to render after the body section</td>
</tr>
<tr>
<td><code>beforeFooter</code></td>
<td><code>Array[tooltipItem], data</code></td>
<td>Returns text to render before the footer section.</td>
</tr>
<tr>
<td><code>footer</code></td>
<td><code>Array[tooltipItem], data</code></td>
<td>Returns text to render as the footer of the tooltip.</td>
</tr>
<tr>
<td><code>afterFooter</code></td>
<td><code>Array[tooltipItem], data</code></td>
<td>Text to render after the footer section</td>
</tr>
</tbody>
</table>
<h3 id="label-color-callback">Label Color Callback</h3>
<p>For example, to return a red box for each item in the tooltip you could do:</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">var</span> chart = <span class="hljs-keyword">new</span> Chart(ctx, {
    type: <span class="hljs-string">&apos;line&apos;</span>,
    data: data,
    options: {
        tooltips: {
            callbacks: {
                labelColor: <span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params">tooltipItem, chart</span>) </span>{
                    <span class="hljs-keyword">return</span> {
                        borderColor: <span class="hljs-string">&apos;rgb(255, 0, 0)&apos;</span>,
                        backgroundColor: <span class="hljs-string">&apos;rgb(255, 0, 0)&apos;</span>
                    }
                },
                labelTextColor:<span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params">tooltipItem, chart</span>)</span>{
                    <span class="hljs-keyword">return</span> <span class="hljs-string">&apos;#543453&apos;</span>;
                }
            }
        }
    }
});
</code></pre>
<h3 id="tooltip-item-interface">Tooltip Item Interface</h3>
<p>The tooltip items passed to the tooltip callbacks implement the following interface.</p>
<pre><code class="lang-javascript">{
    <span class="hljs-comment">// X Value of the tooltip as a string</span>
    xLabel: <span class="hljs-built_in">String</span>,

    <span class="hljs-comment">// Y value of the tooltip as a string</span>
    yLabel: <span class="hljs-built_in">String</span>,

    <span class="hljs-comment">// Index of the dataset the item comes from</span>
    datasetIndex: <span class="hljs-built_in">Number</span>,

    <span class="hljs-comment">// Index of this data item in the dataset</span>
    index: <span class="hljs-built_in">Number</span>,

    <span class="hljs-comment">// X position of matching point</span>
    x: <span class="hljs-built_in">Number</span>,

    <span class="hljs-comment">// Y position of matching point</span>
    y: <span class="hljs-built_in">Number</span>,
}
</code></pre>
<h2 id="external-custom-tooltips">External (Custom) Tooltips</h2>
<p>Custom tooltips allow you to hook into the tooltip rendering process so that you can render the tooltip in your own custom way. Generally this is used to create an HTML tooltip instead of an oncanvas one. You can enable custom tooltips in the global or chart configuration like so:</p>
<pre><code class="lang-javascript"><span class="hljs-keyword">var</span> myPieChart = <span class="hljs-keyword">new</span> Chart(ctx, {
    type: <span class="hljs-string">&apos;pie&apos;</span>,
    data: data,
    options: {
        tooltips: {
            custom: <span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params">tooltipModel</span>) </span>{
                <span class="hljs-comment">// Tooltip Element</span>
                <span class="hljs-keyword">var</span> tooltipEl = <span class="hljs-built_in">document</span>.getElementById(<span class="hljs-string">&apos;chartjs-tooltip&apos;</span>);

                <span class="hljs-comment">// Create element on first render</span>
                <span class="hljs-keyword">if</span> (!tooltipEl) {
                    tooltipEl = <span class="hljs-built_in">document</span>.createElement(<span class="hljs-string">&apos;div&apos;</span>);
                    tooltipEl.id = <span class="hljs-string">&apos;chartjs-tooltip&apos;</span>;
                    tooltipEl.innerHTML = <span class="hljs-string">&quot;&lt;table&gt;&lt;/table&gt;&quot;</span>
                    <span class="hljs-built_in">document</span>.body.appendChild(tooltipEl);
                }

                <span class="hljs-comment">// Hide if no tooltip</span>
                <span class="hljs-keyword">if</span> (tooltipModel.opacity === <span class="hljs-number">0</span>) {
                    tooltipEl.style.opacity = <span class="hljs-number">0</span>;
                    <span class="hljs-keyword">return</span>;
                }

                <span class="hljs-comment">// Set caret Position</span>
                tooltipEl.classList.remove(<span class="hljs-string">&apos;above&apos;</span>, <span class="hljs-string">&apos;below&apos;</span>, <span class="hljs-string">&apos;no-transform&apos;</span>);
                <span class="hljs-keyword">if</span> (tooltipModel.yAlign) {
                    tooltipEl.classList.add(tooltipModel.yAlign);
                } <span class="hljs-keyword">else</span> {
                    tooltipEl.classList.add(<span class="hljs-string">&apos;no-transform&apos;</span>);
                }

                <span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">getBody</span>(<span class="hljs-params">bodyItem</span>) </span>{
                    <span class="hljs-keyword">return</span> bodyItem.lines;
                }

                <span class="hljs-comment">// Set Text</span>
                <span class="hljs-keyword">if</span> (tooltipModel.body) {
                    <span class="hljs-keyword">var</span> titleLines = tooltipModel.title || [];
                    <span class="hljs-keyword">var</span> bodyLines = tooltipModel.body.map(getBody);

                    <span class="hljs-keyword">var</span> innerHtml = <span class="hljs-string">&apos;&lt;thead&gt;&apos;</span>;

                    titleLines.forEach(<span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params">title</span>) </span>{
                        innerHtml += <span class="hljs-string">&apos;&lt;tr&gt;&lt;th&gt;&apos;</span> + title + <span class="hljs-string">&apos;&lt;/th&gt;&lt;/tr&gt;&apos;</span>;
                    });
                    innerHtml += <span class="hljs-string">&apos;&lt;/thead&gt;&lt;tbody&gt;&apos;</span>;

                    bodyLines.forEach(<span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params">body, i</span>) </span>{
                        <span class="hljs-keyword">var</span> colors = tooltipModel.labelColors[i];
                        <span class="hljs-keyword">var</span> style = <span class="hljs-string">&apos;background:&apos;</span> + colors.backgroundColor;
                        style += <span class="hljs-string">&apos;; border-color:&apos;</span> + colors.borderColor;
                        style += <span class="hljs-string">&apos;; border-width: 2px&apos;</span>;
                        <span class="hljs-keyword">var</span> span = <span class="hljs-string">&apos;&lt;span class=&quot;chartjs-tooltip-key&quot; style=&quot;&apos;</span> + style + <span class="hljs-string">&apos;&quot;&gt;&lt;/span&gt;&apos;</span>;
                        innerHtml += <span class="hljs-string">&apos;&lt;tr&gt;&lt;td&gt;&apos;</span> + span + body + <span class="hljs-string">&apos;&lt;/td&gt;&lt;/tr&gt;&apos;</span>;
                    });
                    innerHtml += <span class="hljs-string">&apos;&lt;/tbody&gt;&apos;</span>;

                    <span class="hljs-keyword">var</span> tableRoot = tooltipEl.querySelector(<span class="hljs-string">&apos;table&apos;</span>);
                    tableRoot.innerHTML = innerHtml;
                }

                <span class="hljs-comment">// `this` will be the overall tooltip</span>
                <span class="hljs-keyword">var</span> position = <span class="hljs-keyword">this</span>._chart.canvas.getBoundingClientRect();

                <span class="hljs-comment">// Display, position, and set styles for font</span>
                tooltipEl.style.opacity = <span class="hljs-number">1</span>;
                tooltipEl.style.left = position.left + tooltipModel.caretX + <span class="hljs-string">&apos;px&apos;</span>;
                tooltipEl.style.top = position.top + tooltipModel.caretY + <span class="hljs-string">&apos;px&apos;</span>;
                tooltipEl.style.fontFamily = tooltipModel._fontFamily;
                tooltipEl.style.fontSize = tooltipModel.fontSize;
                tooltipEl.style.fontStyle = tooltipModel._fontStyle;
                tooltipEl.style.padding = tooltipModel.yPadding + <span class="hljs-string">&apos;px &apos;</span> + tooltipModel.xPadding + <span class="hljs-string">&apos;px&apos;</span>;
            }
        }
    }
});
</code></pre>
<p>See <code>samples/tooltips/line-customTooltips.html</code> for examples on how to get started.</p>
<h2 id="tooltip-model">Tooltip Model</h2>
<p>The tooltip model contains parameters that can be used to render the tooltip.</p>
<pre><code class="lang-javascript">{
    <span class="hljs-comment">// The items that we are rendering in the tooltip. See Tooltip Item Interface section</span>
    dataPoints: TooltipItem[],

    <span class="hljs-comment">// Positioning</span>
    xPadding: <span class="hljs-built_in">Number</span>,
    yPadding: <span class="hljs-built_in">Number</span>,
    xAlign: <span class="hljs-built_in">String</span>,
    yAlign: <span class="hljs-built_in">String</span>,

    <span class="hljs-comment">// X and Y properties are the top left of the tooltip</span>
    x: <span class="hljs-built_in">Number</span>,
    y: <span class="hljs-built_in">Number</span>,
    width: <span class="hljs-built_in">Number</span>,
    height: <span class="hljs-built_in">Number</span>,
    <span class="hljs-comment">// Where the tooltip points to</span>
    caretX: <span class="hljs-built_in">Number</span>,
    caretY: <span class="hljs-built_in">Number</span>,

    <span class="hljs-comment">// Body</span>
    <span class="hljs-comment">// The body lines that need to be rendered</span>
    <span class="hljs-comment">// Each object contains 3 parameters</span>
    <span class="hljs-comment">// before: String[] // lines of text before the line with the color square</span>
    <span class="hljs-comment">// lines: String[], // lines of text to render as the main item with color square</span>
    <span class="hljs-comment">// after: String[], // lines of text to render after the main lines</span>
    body: <span class="hljs-built_in">Object</span>[],
    <span class="hljs-comment">// lines of text that appear after the title but before the body</span>
    beforeBody: <span class="hljs-built_in">String</span>[],
    <span class="hljs-comment">// line of text that appear after the body and before the footer</span>
    afterBody: <span class="hljs-built_in">String</span>[],
    bodyFontColor: Color,
    _bodyFontFamily: <span class="hljs-built_in">String</span>,
    _bodyFontStyle: <span class="hljs-built_in">String</span>,
    _bodyAlign: <span class="hljs-built_in">String</span>,
    bodyFontSize: <span class="hljs-built_in">Number</span>,
    bodySpacing: <span class="hljs-built_in">Number</span>,

    <span class="hljs-comment">// Title</span>
    <span class="hljs-comment">// lines of text that form the title</span>
    title: <span class="hljs-built_in">String</span>[],
    titleFontColor: Color,
    _titleFontFamily: <span class="hljs-built_in">String</span>,
    _titleFontStyle: <span class="hljs-built_in">String</span>,
    titleFontSize: <span class="hljs-built_in">Number</span>,
    _titleAlign: <span class="hljs-built_in">String</span>,
    titleSpacing: <span class="hljs-built_in">Number</span>,
    titleMarginBottom: <span class="hljs-built_in">Number</span>,

    <span class="hljs-comment">// Footer</span>
    <span class="hljs-comment">// lines of text that form the footer</span>
    footer: <span class="hljs-built_in">String</span>[],
    footerFontColor: Color,
    _footerFontFamily: <span class="hljs-built_in">String</span>,
    _footerFontStyle: <span class="hljs-built_in">String</span>,
    footerFontSize: <span class="hljs-built_in">Number</span>,
    _footerAlign: <span class="hljs-built_in">String</span>,
    footerSpacing: <span class="hljs-built_in">Number</span>,
    footerMarginTop: <span class="hljs-built_in">Number</span>,

    <span class="hljs-comment">// Appearance</span>
    caretSize: <span class="hljs-built_in">Number</span>,
    cornerRadius: <span class="hljs-built_in">Number</span>,
    backgroundColor: Color,

    <span class="hljs-comment">// colors to render for each item in body[]. This is the color of the squares in the tooltip</span>
    labelColors: Color[],

    <span class="hljs-comment">// 0 opacity is a hidden tooltip</span>
    opacity: <span class="hljs-built_in">Number</span>,
    legendColorBackground: Color,
    displayColors: <span class="hljs-built_in">Boolean</span>,
}
</code></pre>

                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="title.html" class="navigation navigation-prev " aria-label="Previous page: Title">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="elements.html" class="navigation navigation-next " aria-label="Next page: Elements">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Tooltip","level":"1.4.5","depth":2,"next":{"title":"Elements","level":"1.4.6","depth":2,"path":"configuration/elements.md","ref":"configuration/elements.md","articles":[]},"previous":{"title":"Title","level":"1.4.4","depth":2,"path":"configuration/title.md","ref":"configuration/title.md","articles":[]},"dir":"ltr"},"config":{"plugins":["-lunr","-search","search-plus","anchorjs","chartjs","ga"],"root":"./docs","styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"anchorjs":{"icon":"#","placement":"left","visible":"always"},"ga":{"configuration":"auto","token":"UA-28909194-3"},"theme-default":{"styles":{"website":"style.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"search-plus":{},"chartjs":{"defaults":null},"highlight":{},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"fontsettings":{"theme":"white","family":"sans","size":2}},"theme":"default","author":"chartjs","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"gitbook":"3.2.2"},"file":{"path":"configuration/tooltip.md","mtime":"2017-10-28T15:03:49.266Z","type":"markdown"},"gitbook":{"version":"3.2.2","time":"2017-10-28T15:09:53.587Z"},"basePath":"..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="../gitbook/gitbook.js"></script>
    <script src="../gitbook/theme.js"></script>
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/jquery.mark.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-search-plus/search.js"></script>
        
    
        
        <script src="https://cdnjs.cloudflare.com/ajax/libs/anchor-js/3.1.1/anchor.min.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-anchorjs/anchor-style.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-ga/plugin.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-sharing/buttons.js"></script>
        
    
        
        <script src="../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>
        
    

    </body>
</html>

