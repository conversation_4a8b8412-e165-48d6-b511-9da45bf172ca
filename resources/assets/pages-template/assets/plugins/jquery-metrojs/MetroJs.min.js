!function(l){l.fn.metrojs={capabilities:null,checkCapabilities:function(e,t){return(null==l.fn.metrojs.capabilities||"undefined"!=typeof t&&1==t)&&(l.fn.metrojs.capabilities=new l.fn.metrojs.MetroModernizr(e)),l.fn.metrojs.capabilities}};var e=l.fn.metrojs,t=99e3;l.fn.liveTile=function(e){if(n[e]){for(var t=[],i=1;i<=arguments.length;i++)t[i-1]=arguments[i];return n[e].apply(this,t)}return"object"!=typeof e&&e?(l.error("Method "+e+" does not exist on jQuery.liveTile"),null):n.init.apply(this,arguments)},l.fn.liveTile.contentModules={modules:[],addContentModule:function(l,e){this.modules instanceof Array||(this.modules=[]),this.modules.push(e)},hasContentModule:function(l){if("undefined"==typeof l||!(this.modules instanceof Array))return-1;for(var e=0;e<this.modules.length;e++)if("undefined"!=typeof this.modules[e].moduleName&&this.modules[e].moduleName==l)return e;return-1}},l.fn.liveTile.defaults={mode:"slide",speed:500,initDelay:-1,delay:5e3,stops:"100%",stack:!1,direction:"vertical",animationDirection:"forward",tileSelector:">div,>li,>p,>img,>a",tileFaceSelector:">div,>li,>p,>img,>a",ignoreDataAttributes:!1,click:null,link:"",newWindow:!1,bounce:!1,bounceDirections:"all",bounceFollowsMove:!0,pauseOnHover:!1,pauseOnHoverEvent:"both",playOnHover:!1,playOnHoverEvent:"both",onHoverDelay:0,repeatCount:-1,appendBack:!0,alwaysTrigger:!1,flipListOnHover:!1,flipListOnHoverEvent:"mouseout",noHAflipOpacity:"1",haTransFunc:"ease",noHaTransFunc:"linear",currentIndex:0,startNow:!0,useModernizr:"undefined"!=typeof window.Modernizr,useHardwareAccel:!0,useTranslate:!0,faces:{$front:null,$back:null},animationStarting:function(){},animationComplete:function(){},triggerDelay:function(){return 3e3*Math.random()},swap:"",swapFront:"-",swapBack:"-",contentModules:[]};var n={init:function(t){var n=l.extend({},l.fn.liveTile.defaults,t);return e.checkCapabilities(n),o.getBrowserPrefix(),-1==l.fn.liveTile.contentModules.hasContentModule("image")&&l.fn.liveTile.contentModules.addContentModule("image",a.imageSwap),-1==l.fn.liveTile.contentModules.hasContentModule("html")&&l.fn.liveTile.contentModules.addContentModule("html",a.htmlSwap),l(this).each(function(e,t){var o=l(t),a=i.initTileData(o,n);a.faces=i.prepTile(o,a),a.fade=function(l){i.fade(o,l)},a.slide=function(l){i.slide(o,l)},a.carousel=function(l){i.carousel(o,l)},a.flip=function(l){i.flip(o,l)},a.flipList=function(l){i.flipList(o,l)};var r={fade:a.fade,slide:a.slide,carousel:a.carousel,flip:a.flip,"flip-list":a.flipList};a.doAction=function(l){var e=r[a.mode];"function"==typeof e&&(e(l),a.hasRun=!0),l==a.timer.repeatCount&&(a.runEvents=!1)},a.timer=new l.fn.metrojs.TileTimer(a.delay,a.doAction,a.repeatCount),o.data("LiveTile",a),("flip-list"!==a.mode||0==a.flipListOnHover)&&(a.pauseOnHover?i.bindPauseOnHover(o):a.playOnHover&&i.bindPlayOnHover(o,a)),(a.link.length>0||"function"==typeof a.click)&&o.css({cursor:"pointer"}).bind("click.liveTile",function(l){var e=!0;"function"==typeof a.click&&(e=a.click(o,a)||!1),e&&a.link.length>0&&(l.preventDefault(),a.newWindow?window.open(a.link):window.location=a.link)}),i.bindBounce(o,a),a.startNow&&"none"!=a.mode&&(a.runEvents=!0,a.timer.start(a.initDelay))})},"goto":function(e){var t,n=typeof e;if("undefined"===n&&(t={index:-99,delay:0,autoAniDirection:!1}),"number"!==n&&isNaN(e)){if("string"===n)if("next"==e)t={index:-99,delay:0};else{if(0!==e.indexOf("prev"))return l.error(e+' is not a recognized action for .liveTile("goto")'),l(this);t={index:-100,delay:0}}else if("object"===n){"undefined"==typeof e.delay&&(e.delay=0);var i=typeof e.index;"undefined"===i?e.index=0:"string"===i&&("next"===e.index?e.index=-99:0===e.index.indexOf("prev")&&(e.index=-100)),t=e}}else t={index:parseInt(e,10),delay:0};return l(this).each(function(e,n){var i=l(n),o=i.data("LiveTile"),a=i.data("metrojs.tile"),r=t.index;if(a.animating===!0)return l(this);if("carousel"===o.mode){var s=o.faces.$listTiles.filter(".active"),c=o.faces.$listTiles.index(s);if(-100===r?(("undefined"==typeof t.autoAniDirection||1==t.autoAniDirection)&&(o.tempValues.animationDirection="undefined"==typeof t.animationDirection?"backward":t.animationDirection),r=0===c?o.faces.$listTiles.length-1:c-1):-99===r&&(("undefined"==typeof t.autoAniDirection||1==t.autoAniDirection)&&(o.tempValues.animationDirection="undefined"==typeof t.animationDirection?"forward":t.animationDirection),r=c+1),c==r)return;"undefined"!=typeof t.direction&&(o.tempValues.direction=t.direction),"undefined"!=typeof t.animationDirection&&(o.tempValues.animationDirection=t.animationDirection),o.currentIndex=0==r?o.faces.$listTiles.length:r-1}else o.currentIndex=r;o.runEvents=!0,o.timer.start(t.delay>=0?t.delay:o.delay)})},play:function(e){var t,n=typeof e;return"undefined"===n?t={delay:-1}:"number"===n?t={delay:e}:"object"===n&&("undefined"==typeof e.delay&&(e.delay=-1),t=e),l(this).each(function(e,n){var i=l(n),o=i.data("LiveTile");o.runEvents=!0,t.delay<0&&!o.hasRun&&(t.delay=o.initDelay),o.timer.start(t.delay>=0?t.delay:o.delay)})},animate:function(){return l(this).each(function(e,t){var n=l(t),i=n.data("LiveTile");i.doAction()})},stop:function(){return l(this).each(function(e,t){var n=l(t),i=n.data("LiveTile");i.hasRun=!1,i.runEvents=!1,i.timer.stop(),window.clearTimeout(i.eventTimeout),window.clearTimeout(i.flCompleteTimeout),window.clearTimeout(i.completeTimeout),"flip-list"===i.mode&&i.faces.$listTiles.each(function(e,t){var n=l(t).data("metrojs.tile");window.clearTimeout(n.eventTimeout),window.clearTimeout(n.flCompleteTimeout),window.clearTimeout(n.completeTimeout)})})},pause:function(){return l(this).each(function(e,t){var n=l(t),i=n.data("LiveTile");i.timer.pause(),i.runEvents=!1,window.clearTimeout(i.eventTimeout),window.clearTimeout(i.flCompleteTimeout),window.clearTimeout(i.completeTimeout),"flip-list"===i.mode&&i.faces.$listTiles.each(function(e,t){var n=l(t).data("metrojs.tile");window.clearTimeout(n.eventTimeout),window.clearTimeout(n.flCompleteTimeout),window.clearTimeout(n.completeTimeout)})})},restart:function(e){var t,n=typeof e;return"undefined"===n?t={delay:-1}:"number"===n?t={delay:e}:"object"===n&&("undefined"==typeof e.delay&&(e.delay=-1),t=e),l(this).each(function(e,n){var i=l(n),o=i.data("LiveTile");t.delay<0&&!o.hasRun&&(t.delay=o.initDelay),o.hasRun=!1,o.runEvents=!0,o.timer.restart(t.delay>=0?t.delay:o.delay)})},rebind:function(e){return l(this).each(function(l,t){"undefined"!=typeof e?("undefined"!=typeof e.timer&&null!=e.timer&&e.timer.stop(),e.hasRun=!1,n.init.apply(t,e)):n.init.apply(t,{})})},destroy:function(e){var t,n=typeof e;return"undefined"===n?t={removeCss:!1}:"boolean"===n?t={removeCss:e}:"object"===n&&("undefined"==typeof e.removeCss&&(e.removeCss=!1),t=e),l(this).each(function(e,n){var a=l(n),r=a.data("LiveTile");if("undefined"!=typeof r){a.unbind(".liveTile");var s=o.appendStyleProperties({margin:"",cursor:""},["transform","transition"],["",""]);r.timer.stop(),window.clearTimeout(r.eventTimeout),window.clearTimeout(r.flCompleteTimeout),window.clearTimeout(r.completeTimeout),null!=r.faces.$listTiles&&r.faces.$listTiles.each(function(e,n){var o=l(n);if("flip-list"===r.mode){var a=o.data("metrojs.tile");window.clearTimeout(a.eventTimeout),window.clearTimeout(a.flCompleteTimeout),window.clearTimeout(a.completeTimeout)}else if("carousel"===r.mode){var c=r.listData[e];c.bounce&&i.unbindMsBounce(o,c)}t.removeCss?(o.removeClass("ha"),o.find(r.tileFaceSelector).unbind(".liveTile").removeClass("bounce flip-front flip-back ha slide slide-front slide-back").css(s)):o.find(r.tileFaceSelector).unbind(".liveTile"),o.removeData("metrojs.tile")}).unbind(".liveTile"),null!=r.faces.$front&&t.removeCss&&r.faces.$front.removeClass("flip-front flip-back ha slide slide-front slide-back").css(s),null!=r.faces.$back&&t.removeCss&&r.faces.$back.removeClass("flip-front flip-back ha slide slide-front slide-back").css(s),r.bounce&&i.unbindMsBounce(a,r),r.playOnHover&&i.unbindMsPlayOnHover(a,r),r.pauseOnhover&&i.unbindMsPauseOnHover(a,r),a.removeClass("ha"),a.removeData("LiveTile"),a.removeData("metrojs.tile"),r=null}})}},i={dataAtr:function(l,e,t){return"undefined"!=typeof l.attr("data-"+e)?l.attr("data-"+e):t},dataMethod:function(l,e,t){return"undefined"!=typeof l.data(e)?l.data(e):t},getDataOrDefault:null,initTileData:function(t,n){var o=0==n.ignoreDataAttributes,a=null;null==this.getDataOrDefault&&(this.getDataOrDefault=e.capabilities.isOldJQuery?this.dataAtr:this.dataMethod),a=o?{speed:this.getDataOrDefault(t,"speed",n.speed),delay:this.getDataOrDefault(t,"delay",n.delay),stops:this.getDataOrDefault(t,"stops",n.stops),stack:this.getDataOrDefault(t,"stack",n.stack),mode:this.getDataOrDefault(t,"mode",n.mode),direction:this.getDataOrDefault(t,"direction",n.direction),useHardwareAccel:this.getDataOrDefault(t,"ha",n.useHardwareAccel),repeatCount:this.getDataOrDefault(t,"repeat",n.repeatCount),swap:this.getDataOrDefault(t,"swap",n.swap),appendBack:this.getDataOrDefault(t,"appendback",n.appendBack),currentIndex:this.getDataOrDefault(t,"start-index",n.currentIndex),animationDirection:this.getDataOrDefault(t,"ani-direction",n.animationDirection),startNow:this.getDataOrDefault(t,"start-now",n.startNow),tileSelector:this.getDataOrDefault(t,"tile-selector",n.tileSelector),tileFaceSelector:this.getDataOrDefault(t,"face-selector",n.tileFaceSelector),bounce:this.getDataOrDefault(t,"bounce",n.bounce),bounceDirections:this.getDataOrDefault(t,"bounce-dir",n.bounceDirections),bounceFollowsMove:this.getDataOrDefault(t,"bounce-follows",n.bounceFollowsMove),click:this.getDataOrDefault(t,"click",n.click),link:this.getDataOrDefault(t,"link",n.link),newWindow:this.getDataOrDefault(t,"new-window",n.newWindow),alwaysTrigger:this.getDataOrDefault(t,"always-trigger",n.alwaysTrigger),flipListOnHover:this.getDataOrDefault(t,"flip-onhover",n.flipListOnHover),pauseOnHover:this.getDataOrDefault(t,"pause-onhover",n.pauseOnHover),playOnHover:this.getDataOrDefault(t,"play-onhover",n.playOnHover),onHoverDelay:this.getDataOrDefault(t,"hover-delay",n.onHoverDelay),noHAflipOpacity:this.getDataOrDefault(t,"flip-opacity",n.noHAflipOpacity),useTranslate:this.getDataOrDefault(t,"use-translate",n.useTranslate),runEvents:!1,isReversed:!1,loopCount:0,contentModules:[],listData:[],height:t.height(),width:t.width(),tempValues:{}}:l.extend(!0,{runEvents:!1,isReversed:!1,loopCount:0,contentModules:[],listData:[],height:t.height(),width:t.width(),tempValues:{}},n),a.useTranslate=a.useTranslate&&a.useHardwareAccel&&e.capabilities.canTransform&&e.capabilities.canTransition,a.margin="vertical"===a.direction?a.height/2:a.width/2,a.stops="object"==typeof n.stops&&n.stops instanceof Array?n.stops:(""+a.stops).split(","),1===a.stops.length&&a.stops.push("0px");var r=a.swap.replace(" ","").split(","),s=o?this.getDataOrDefault(t,"swap-front",n.swapFront):n.swapFront,c=o?this.getDataOrDefault(t,"swap-back",n.swapBack):n.swapBack;a.swapFront="-"===s?r:s.replace(" ","").split(","),a.swapBack="-"===c?r:c.replace(" ","").split(",");var d;for(d=0;d<a.swapFront.length;d++)a.swapFront[d].length>0&&-1===l.inArray(a.swapFront[d],r)&&r.push(a.swapFront[d]);for(d=0;d<a.swapBack.length;d++)a.swapBack[d].length>0&&-1===l.inArray(a.swapBack[d],r)&&r.push(a.swapBack[d]);for(a.swap=r,d=0;d<r.length;d++)if(r[d].length>0){var u=l.fn.liveTile.contentModules.hasContentModule(r[d]);u>-1&&a.contentModules.push(l.fn.liveTile.contentModules.modules[u])}a.initDelay=o?this.getDataOrDefault(t,"initdelay",n.initDelay):n.initDelay,a.delay<-1?a.delay=n.triggerDelay(1):a.delay<0&&(a.delay=3500+4501*Math.random()),a.initDelay<0&&(a.initDelay=a.delay);var h={};for(d=0;d<a.contentModules.length;d++)l.extend(h,a.contentModules[d].data);l.extend(h,n,a);var f;for("flip-list"===h.mode?(f=t.find(h.tileSelector).not(".tile-title"),f.each(function(e,t){var n=l(t),a={direction:o?i.getDataOrDefault(n,"direction",h.direction):h.direction,newWindow:o?i.getDataOrDefault(n,"new-window",!1):!1,link:o?i.getDataOrDefault(n,"link",""):"",faces:{$front:null,$back:null},height:n.height(),width:n.width(),isReversed:!1};a.margin="vertical"===a.direction?a.height/2:a.width/2,h.listData.push(a)})):"carousel"===h.mode&&(h.stack=!0,f=t.find(h.tileSelector).not(".tile-title"),f.each(function(e,t){var n=l(t),a={bounce:o?i.getDataOrDefault(n,"bounce",!1):!1,bounceDirections:o?i.getDataOrDefault(n,"bounce-dir","all"):"all",link:o?i.getDataOrDefault(n,"link",""):"",newWindow:o?i.getDataOrDefault(n,"new-window",!1):!1,animationDirection:o?i.getDataOrDefault(n,"ani-direction",""):"",direction:o?i.getDataOrDefault(n,"direction",""):""};h.listData.push(a)})),d=0;d<a.contentModules.length;d++)"function"==typeof h.contentModules[d].initData&&h.contentModules[d].initData(h,t);return a=null,h},prepTile:function(n,a){n.addClass(a.mode);var r,s,c,d,u={$tileFaces:null,$listTiles:null,$front:null,$back:null};switch(a.mode){case"fade":u.$tileFaces=n.find(a.tileFaceSelector).not(".tile-title"),u.$front=null!=a.faces.$front&&a.faces.$front.length>0?a.faces.$front.addClass("fade-front"):u.$tileFaces.filter(":first").addClass("fade-front"),u.$back=null!=a.faces.$back&&a.faces.$back.length>0?a.faces.$back.addClass("fade-back"):u.$tileFaces.length>1?u.$tileFaces.filter(":last").addClass("fade-back"):a.appendBack?l('<div class="fade-back"></div>').appendTo(n):l("<div></div>");break;case"slide":if(u.$tileFaces=n.find(a.tileFaceSelector).not(".tile-title"),u.$front=null!=a.faces.$front&&a.faces.$front.length>0?a.faces.$front.addClass("slide-front"):u.$tileFaces.filter(":first").addClass("slide-front"),u.$back=null!=a.faces.$back&&a.faces.$back.length>0?a.faces.$back.addClass("slide-back"):u.$tileFaces.length>1?u.$tileFaces.filter(":last").addClass("slide-back"):a.appendBack?l('<div class="slide-back"></div>').appendTo(n):l("<div></div>"),1==a.stack){var h,f;"vertical"===a.direction?(h="top",f="translate(0%, -100%) translateZ(0)"):(h="left",f="translate(-100%, 0%) translateZ(0)"),c={},a.useTranslate?o.appendStyleProperties(c,["transform"],[f]):c[h]="-100%",u.$back.css(c)}n.data("metrojs.tile",{animating:!1}),e.capabilities.canTransition&&a.useHardwareAccel&&(n.addClass("ha"),u.$front.addClass("ha"),u.$back.addClass("ha"));break;case"carousel":u.$listTiles=n.find(a.tileSelector).not(".tile-title");var p=u.$listTiles.length;n.data("metrojs.tile",{animating:!1}),a.currentIndex=Math.min(a.currentIndex,p-1),u.$listTiles.each(function(t,n){var r=l(n).addClass("slide"),s=a.listData[t],c="string"==typeof s.animationDirection&&s.animationDirection.length>0?s.animationDirection:a.animationDirection,u="string"==typeof s.direction&&s.direction.length>0?s.direction:a.direction;t==a.currentIndex?r.addClass("active"):"forward"===c?"vertical"===u?(d=a.useTranslate?o.appendStyleProperties({},["transform"],["translate(0%, 100%) translateZ(0)"]):{left:"0%",top:"100%"},r.css(d)):(d=a.useTranslate?o.appendStyleProperties({},["transform"],["translate(100%, 0%) translateZ(0)"]):{left:"100%",top:"0%"},r.css(d)):"backward"===c&&("vertical"===u?(d=a.useTranslate?o.appendStyleProperties({},["transform"],["translate(0%, -100%) translateZ(0)"]):{left:"0%",top:"-100%"},r.css(d)):(d=a.useTranslate?o.appendStyleProperties({},["transform"],["translate(-100%, 0%) translateZ(0)"]):{left:"-100%",top:"0%"},r.css(d))),i.bindLink(r,s),a.useHardwareAccel&&e.capabilities.canTransition&&i.bindBounce(r,s),r=null,s=null}),e.capabilities.canFlip3d&&a.useHardwareAccel&&(n.addClass("ha"),u.$listTiles.addClass("ha"));break;case"flip-list":u.$listTiles=n.find(a.tileSelector).not(".tile-title"),u.$listTiles.each(function(n,d){var u=l(d).addClass("tile-"+(n+1)),h=u.find(a.tileFaceSelector).filter(":first").addClass("flip-front").css({margin:"0px"});1===u.find(a.tileFaceSelector).length&&1==a.appendBack&&u.append("<div></div>");var f=u.find(a.tileFaceSelector).filter(":last").addClass("flip-back").css({margin:"0px"});a.listData[n].faces.$front=h,a.listData[n].faces.$back=f,u.data("metrojs.tile",{animating:!1,count:1,completeTimeout:null,flCompleteTimeout:null,index:n});var p=u.data("metrojs.tile");e.capabilities.canFlip3d&&a.useHardwareAccel?(u.addClass("ha"),h.addClass("ha"),f.addClass("ha"),r="vertical"===a.listData[n].direction?"rotateX(180deg)":"rotateY(180deg)",c=o.appendStyleProperties({},["transform"],[r]),f.css(c)):(s="vertical"===a.listData[n].direction?{height:"100%",width:"100%",marginTop:"0px",opacity:"1"}:{height:"100%",width:"100%",marginLeft:"0px",opacity:"1"},c="vertical"===a.listData[n].direction?{height:"0px",width:"100%",marginTop:a.listData[n].margin+"px",opacity:a.noHAflipOpacity}:{height:"100%",width:"0px",marginLeft:a.listData[n].margin+"px",opacity:a.noHAflipOpacity},h.css(s),f.css(c));var m=function(){p.count++,p.count>=t&&(p.count=1)};if(a.flipListOnHover){var g=a.flipListOnHoverEvent+".liveTile";h.bind(g,function(){i.flip(u,p.count,a,m)}),f.bind(g,function(){i.flip(u,p.count,a,m)})}a.listData[n].link.length>0&&u.css({cursor:"pointer"}).bind("click.liveTile",function(){a.listData[n].newWindow?window.open(a.listData[n].link):window.location=a.listData[n].link})});break;case"flip":u.$tileFaces=n.find(a.tileFaceSelector).not(".tile-title"),u.$front=null!=a.faces.$front&&a.faces.$front.length>0?a.faces.$front.addClass("flip-front"):u.$tileFaces.filter(":first").addClass("flip-front"),u.$back=null!=a.faces.$back&&a.faces.$back.length>0?a.faces.$back.addClass("flip-back"):u.$tileFaces.length>1?u.$tileFaces.filter(":last").addClass("flip-back"):a.appendBack?l('<div class="flip-back"></div>').appendTo(n):l("<div></div>"),n.data("metrojs.tile",{animating:!1}),e.capabilities.canFlip3d&&a.useHardwareAccel?(n.addClass("ha"),u.$front.addClass("ha"),u.$back.addClass("ha"),r="vertical"===a.direction?"rotateX(180deg)":"rotateY(180deg)",c=o.appendStyleProperties({},["transform"],[r]),u.$back.css(c)):(s="vertical"===a.direction?{height:"100%",width:"100%",marginTop:"0px",opacity:"1"}:{height:"100%",width:"100%",marginLeft:"0px",opacity:"1"},c="vertical"===a.direction?{height:"0%",width:"100%",marginTop:a.margin+"px",opacity:"0"}:{height:"100%",width:"0%",marginLeft:a.margin+"px",opacity:"0"},u.$front.css(s),u.$back.css(c))}return u},bindPauseOnHover:function(t){!function(){var n=t.data("LiveTile"),i=!1,o=!1,a="both"==n.pauseOnHoverEvent||"mouseover"==n.pauseOnHoverEvent||"mouseenter"==n.pauseOnHoverEvent,r="both"==n.pauseOnHoverEvent||"mouseout"==n.pauseOnHoverEvent||"mouseleave"==n.pauseOnHoverEvent;n.pOnHoverMethods={pause:function(){n.timer.pause(),"flip-list"===n.mode&&n.faces.$listTiles.each(function(e,t){window.clearTimeout(l(t).data("metrojs.tile").completeTimeout)})},over:function(){i||o||n.runEvents&&(o=!0,n.eventTimeout=window.setTimeout(function(){o=!1,r&&(i=!0),n.pOnHoverMethods.pause()},n.onHoverDelay))},out:function(){if(o)return window.clearTimeout(n.eventTimeout),o=!1,void 0;if(a){if(!i&&!o)return;n.runEvents&&n.timer.start(n.hasRun?n.delay:n.initDelay)}else n.pOnHoverMethods.pause();i=!1}},e.capabilities.canTouch?window.navigator.msPointerEnabled?(a&&t[0].addEventListener("MSPointerOver",n.pOnHoverMethods.over,!1),r&&t[0].addEventListener("MSPointerOut",n.pOnHoverMethods.out,!1)):(a&&t.bind("touchstart.liveTile",n.pOnHoverMethods.over),r&&t.bind("touchend.liveTile",n.pOnHoverMethods.out)):(a&&t.bind("mouseover.liveTile",n.pOnHoverMethods.over),r&&t.bind("mouseout.liveTile",n.pOnHoverMethods.out))}()},unbindMsPauseOnHover:function(l,e){"undefined"!=typeof e.pOnHoverMethods&&window.navigator.msPointerEnabled&&(l[0].removeEventListener("MSPointerOver",e.pOnHoverMethods.over,!1),l[0].removeEventListener("MSPointerOut",e.pOnHoverMethods.out,!1))},bindPlayOnHover:function(l,t){!function(){var i=!1,o=!1,a="both"==t.playOnHoverEvent||"mouseover"==t.playOnHoverEvent||"mouseenter"==t.playOnHoverEvent,r="both"==t.playOnHoverEvent||"mouseout"==t.playOnHoverEvent||"mouseleave"==t.playOnHoverEvent;t.onHoverMethods={over:function(){if(!(i||o||t.bounce&&"no"!=t.bounceMethods.down)){var e="flip"==t.mode||(t.startNow?!t.isReversed:t.isReversed);window.clearTimeout(t.eventTimeout),(t.runEvents&&e||!t.hasRun)&&(o=!0,t.eventTimeout=window.setTimeout(function(){o=!1,r&&(i=!0),n.play.apply(l[0],[0])},t.onHoverDelay))}},out:function(){return o?(window.clearTimeout(t.eventTimeout),o=!1,void 0):((!a||i||o)&&(window.clearTimeout(t.eventTimeout),t.eventTimeout=window.setTimeout(function(){var e="flip"==t.mode||(t.startNow?t.isReversed:!t.isReversed);t.runEvents&&e&&n.play.apply(l[0],[0]),i=!1},t.speed+200)),void 0)}},e.capabilities.canTouch?window.navigator.msPointerEnabled?(a&&l[0].addEventListener("MSPointerDown",t.onHoverMethods.over,!1),r&&l.bind("mouseleave.liveTile",t.onHoverMethods.out)):(a&&l.bind("touchstart.liveTile",t.onHoverMethods.over),r&&l.bind("touchend.liveTile",t.onHoverMethods.out)):(a&&l.bind("mouseenter.liveTile",t.onHoverMethods.over),r&&l.bind("mouseleave.liveTile",t.onHoverMethods.out))}()},unbindMsPlayOnHover:function(l,e){"undefined"!=typeof e.onHoverMethods&&window.navigator.msPointerEnabled&&l[0].removeEventListener("MSPointerDown",e.onHoverMethods.over,!1)},bindBounce:function(t,n){n.bounce&&(t.addClass("bounce"),t.addClass("noselect"),function(){n.bounceMethods={down:"no",threshold:30,zeroPos:{x:0,y:0},eventPos:{x:0,y:0},inTilePos:{x:0,y:0},pointPos:{x:0,y:0},regions:{c:[0,0],tl:[-1,-1],tr:[1,-1],bl:[-1,1],br:[1,1],t:[null,-1],r:[1,null],b:[null,1],l:[-1,null]},targets:{all:["c","t","r","b","l","tl","tr","bl","br"],edges:["c","t","r","b","l"],corners:["c","tl","tr","bl","br"]},hitTest:function(t,i,o,a){var r=n.bounceMethods.regions,s=n.bounceMethods.targets[o],c=0,d=null,u=null,h={hit:[0,0],name:"c"};if(e.capabilities.isOldAndroid||!e.capabilities.canTransition)return h;"undefined"==typeof s&&("string"==typeof o&&(s=o.split(",")),l.isArray(s)&&-1==l.inArray("c")&&(a=0,h=null));for(var f=t.width(),p=t.height(),m=[f*a,p*a],g=i.x-.5*f,v=i.y-.5*p,b=[g>0?Math.abs(g)<=m[0]?0:1:Math.abs(g)<=m[0]?0:-1,v>0?Math.abs(v)<=m[1]?0:1:Math.abs(v)<=m[1]?0:-1];c<s.length;c++){if(null!=d)return d;var T=s[c],y=r[T];if("*"==T)return T=s[c+1],{region:r[T],name:T};b[0]==y[0]&&b[1]==y[1]?d={hit:y,name:T}:b[0]!=y[0]&&null!=y[0]||b[1]!=y[1]&&null!=y[1]||(u={hit:y,name:T})}return null!=d?d:null!=u?u:h},bounceDown:function(e){if("A"!=e.target.tagName||l(e).is(".bounce")){var i=e.originalEvent&&e.originalEvent.touches?e.originalEvent.touches[0]:e,a=t.offset();window.pageXOffset,window.pageYOffset,n.bounceMethods.pointPos={x:i.pageX,y:i.pageY},n.bounceMethods.inTilePos={x:i.pageX-a.left,y:i.pageY-a.top},n.$tileParent||(n.$tileParent=t.parent());var r=n.$tileParent.offset();n.bounceMethods.eventPos={x:a.left-r.left+t.width()/2,y:a.top-r.top+t.height()/2};var s=n.bounceMethods.hitTest(t,n.bounceMethods.inTilePos,n.bounceDirections,.25);if(null==s)n.bounceMethods.down="no";else{window.navigator.msPointerEnabled?(document.addEventListener("MSPointerUp",n.bounceMethods.bounceUp,!1),t[0].addEventListener("MSPointerUp",n.bounceMethods.bounceUp,!1),document.addEventListener("MSPointerCancel",n.bounceMethods.bounceUp,!1),n.bounceFollowsMove&&t[0].addEventListener("MSPointerMove",n.bounceMethods.bounceMove,!1)):(l(document).bind("mouseup.liveTile, touchend.liveTile, touchcancel.liveTile, dragstart.liveTile",n.bounceMethods.bounceUp),n.bounceFollowsMove&&(t.bind("touchmove.liveTile",n.bounceMethods.bounceMove),t.bind("mousemove.liveTile",n.bounceMethods.bounceMove)));var c="bounce-"+s.name;t.addClass(c),n.bounceMethods.down=c,n.bounceMethods.downPcss=o.appendStyleProperties({},["perspective-origin"],[n.bounceMethods.eventPos.x+"px "+n.bounceMethods.eventPos.y+"px"]),n.$tileParent.css(n.bounceMethods.downPcss)}}},bounceUp:function(){"no"!=n.bounceMethods.down&&(n.bounceMethods.unBounce(),window.navigator.msPointerEnabled?(document.removeEventListener("MSPointerUp",n.bounceMethods.bounceUp,!1),t[0].removeEventListener("MSPointerUp",n.bounceMethods.bounceUp,!1),document.removeEventListener("MSPointerCancel",n.bounceMethods.bounceUp,!1),n.bounceFollowsMove&&t[0].removeEventListener("MSPointerMove",n.bounceMethods.bounceMove,!1)):l(document).unbind("mouseup.liveTile, touchend.liveTile, touchcancel.liveTile, dragstart.liveTile",n.bounceMethods.bounceUp),n.bounceFollowsMove&&(t.unbind("touchmove.liveTile",n.bounceMethods.bounceMove),t.unbind("mousemove.liveTile",n.bounceMethods.bounceMove)))},bounceMove:function(l){if("no"!=n.bounceMethods.down){var e=l.originalEvent&&l.originalEvent.touches?l.originalEvent.touches[0]:l,i=Math.abs(e.pageX-n.bounceMethods.pointPos.x),o=Math.abs(e.pageY-n.bounceMethods.pointPos.y);if(i>n.bounceMethods.threshold||o>n.bounceMethods.threshold){var a=n.bounceMethods.down;n.bounceMethods.bounceDown(l),a!=n.bounceMethods.down&&t.removeClass(a)}}},unBounce:function(){if(t.removeClass(n.bounceMethods.down),"object"==typeof n.bounceMethods.downPcss){var l=["perspective-origin","perspective-origin-x","perspective-origin-y"],e=["","",""];n.bounceMethods.downPcss=o.appendStyleProperties({},l,e),window.setTimeout(function(){n.$tileParent.css(n.bounceMethods.downPcss)},200)}n.bounceMethods.down="no",n.bounceMethods.inTilePos=n.bounceMethods.zeroPos,n.bounceMethods.eventPos=n.bounceMethods.zeroPos}},window.navigator.msPointerEnabled?t[0].addEventListener("MSPointerDown",n.bounceMethods.bounceDown,!1):e.capabilities.canTouch?t.bind("touchstart.liveTile",n.bounceMethods.bounceDown):t.bind("mousedown.liveTile",n.bounceMethods.bounceDown)}())},unbindMsBounce:function(l,e){e.bounce&&window.navigator.msPointerEnabled&&(l[0].removeEventListener("MSPointerDown",e.bounceMethods.bounceDown,!1),l[0].removeEventListener("MSPointerCancel",e.bounceMethods.bounceUp,!1),l[0].removeEventListener("MSPointerOut",e.bounceMethods.bounceUp,!1))},bindLink:function(e,t){t.link.length>0&&e.css({cursor:"pointer"}).bind("click.liveTile",function(e){("A"!=e.target.tagName||l(e).is(".live-tile,.slide,.flip"))&&(t.newWindow?window.open(t.link):window.location=t.link)})},runContenModules:function(l,e,t,n){for(var i=0;i<l.contentModules.length;i++){var o=l.contentModules[i];"function"==typeof o.action&&o.action(l,e,t,n)}},fade:function(l,e,t){var n="object"==typeof t?t:l.data("LiveTile"),o=function(){(n.timer.repeatCount>0||-1==n.timer.repeatCount)&&n.timer.count!=n.timer.repeatCount&&n.timer.start(n.delay)};if(!n.faces.$front.is(":animated")){n.timer.pause();var a=n.loopCount+1;n.isReversed=0===a%2;var r=n.animationStarting.call(l[0],n,n.faces.$front,n.faces.$back);if("undefined"!=typeof r&&0==r)return o(),void 0;n.loopCount=a;var s=function(){o(),i.runContenModules(n,n.faces.$front,n.faces.$back),n.animationComplete.call(l[0],n,n.faces.$front,n.faces.$back)};n.isReversed?n.faces.$front.fadeIn(n.speed,n.noHaTransFunc,s):n.faces.$front.fadeOut(n.speed,n.noHaTransFunc,s)}},slide:function(t,n,a,r,s){var c="object"==typeof a?a:t.data("LiveTile"),d=t.data("metrojs.tile");if(1==d.animating||t.is(":animated"))return c=null,d=null,void 0;var u=function(){(c.timer.repeatCount>0||-1==c.timer.repeatCount)&&c.timer.count!=c.timer.repeatCount&&c.timer.start(c.delay)};if("carousel"!==c.mode){c.isReversed=0!==c.currentIndex%2,c.timer.pause();var h=c.animationStarting.call(t[0],c,c.faces.$front,c.faces.$back);if("undefined"!=typeof h&&0==h)return u(),void 0;c.loopCount=c.loopCount+1}else c.isReversed=!0;var f;f="string"==typeof c.tempValues.direction&&c.tempValues.direction.length>0?c.tempValues.direction:c.direction,c.tempValues.direction=null;var p={},m={},g="undefined"==typeof r?c.currentIndex:r,v=l.trim(c.stops[Math.min(g,c.stops.length-1)]),b=v.indexOf("px"),T=0,y=0,C="vertical"===f?c.height:c.width,E="vertical"===f?"top":"left",D=1==c.stack,I=function(){"undefined"==typeof s?(c.currentIndex=c.currentIndex+1,c.currentIndex>c.stops.length-1&&(c.currentIndex=0)):s(),"carousel"!=c.mode&&u(),i.runContenModules(c,c.faces.$front,c.faces.$back,c.currentIndex),c.animationComplete.call(t[0],c,c.faces.$front,c.faces.$back),c=null,d=null};if(b>0?(y=parseInt(v.substring(0,b),10),T=y-C+"px"):(y=parseInt(v.replace("%",""),10),T=y-100+"%"),e.capabilities.canTransition&&c.useHardwareAccel){if("undefined"!=typeof d.animating&&1==d.animating)return;d.animating=!0;var w=["transition-property","transition-duration","transition-timing-function"],S=[c.useTranslate?"transform":E,c.speed+"ms",c.haTransFunc];S[o.browserPrefix+"transition-property"]=o.browserPrefix+"transform",p=o.appendStyleProperties(p,w,S),m=o.appendStyleProperties(m,w,S);var _,O="vertical"===f,k=O?"top":"left";c.useTranslate?(_=O?"translate(0%, "+v+")":"translate("+v+", 0%)",p=o.appendStyleProperties(p,["transform"],[_+"translateZ(0)"]),D&&(_=O?"translate(0%, "+T+")":"translate("+T+", 0%)",m=o.appendStyleProperties(m,["transform"],[_+"translateZ(0)"]))):(p[k]=v,D&&(m[k]=T)),c.faces.$front.css(p),D&&c.faces.$back.css(m),window.clearTimeout(c.completeTimeout),c.completeTimeout=window.setTimeout(function(){d.animating=!1,I()},c.speed)}else{p[E]=v,m[E]=T,d.animating=!0;var R=c.faces.$front.stop(),x=c.faces.$back.stop();R.animate(p,c.speed,c.noHaTransFunc,function(){d.animating=!1,I()}),D&&x.animate(m,c.speed,c.noHaTransFunc,function(){})}},carousel:function(l,t){var n=l.data("LiveTile"),a=l.data("metrojs.tile");if(1==a.animating||n.faces.$listTiles.length<=1)return a=null,void 0;var r=function(){(n.timer.repeatCount>0||-1==n.timer.repeatCount)&&n.timer.count!=n.timer.repeatCount&&n.timer.start(n.delay)};n.timer.pause();var s=n.faces.$listTiles.filter(".active"),c=n.faces.$listTiles.index(s),d=n.currentIndex,u=d!=c?d:c,h=u+1>=n.faces.$listTiles.length?0:u+1,f=n.listData[h];if(c==h)return a=null,s=null,void 0;var p;p="string"==typeof n.tempValues.animationDirection&&n.tempValues.animationDirection.length>0?n.tempValues.animationDirection:"string"==typeof f.animationDirection&&f.animationDirection.length>0?f.animationDirection:n.animationDirection,n.tempValues.animationDirection=null;var m;"string"==typeof n.tempValues.direction&&n.tempValues.direction.length>0?m=n.tempValues.direction:"string"==typeof f.direction&&f.direction.length>0?(m=f.direction,n.tempValues.direction=m):m=n.direction;var g=n.faces.$listTiles.eq(h),v=n.animationStarting.call(l[0],n,s,g);if("undefined"!=typeof v&&0==v)return r(),void 0;n.loopCount=n.loopCount+1;var b,T=o.appendStyleProperties({},["transition-duration"],["0s"]),y="vertical"===m;"backward"===p?(n.useTranslate&&e.capabilities.canTransition?(b=y?"translate(0%, -100%)":"translate(-100%, 0%)",T=o.appendStyleProperties(T,["transform"],[b+" translateZ(0)"]),n.stops=["100%"]):(y?(T.top="-100%",T.left="0%"):(T.top="0%",T.left="-100%"),n.stops=["100%"]),n.faces.$front=s,n.faces.$back=g):(n.useTranslate&&e.capabilities.canTransition?(b=y?"translate(0%, 100%)":"translate(100%, 0%)",T=o.appendStyleProperties(T,["transform"],[b+" translateZ(0)"])):y?(T.top="100%",T.left="0%"):(T.top="0%",T.left="100%"),n.faces.$front=g,n.faces.$back=s,n.stops=["0%"]),g.css(T),window.setTimeout(function(){s.removeClass("active"),g.addClass("active"),i.slide(l,t,n,0,function(){n.currentIndex=h,a=null,s=null,g=null,r()})},150)},flip:function(l,t,n,a){var r=l.data("metrojs.tile");if(1==r.animating)return r=null,void 0;var s,c,d,u,h,f,p,m="object"==typeof n?n:l.data("LiveTile"),g="undefined"==typeof a,v=0,b=function(){(m.timer.repeatCount>0||-1==m.timer.repeatCount)&&m.timer.count!=m.timer.repeatCount&&m.timer.start(m.delay)};if(g){m.timer.pause();var T=m.loopCount+1;p=0===T%2,m.isReversed=p,s=m.faces.$front,c=m.faces.$back;var y=p?[m,c,s]:[m,s,c],C=m.animationStarting.apply(l[0],y);if("undefined"!=typeof C&&0==C)return b(),void 0;d=m.direction,height=m.height,width=m.width,margin=m.margin,m.loopCount=T}else p=0===t%2,v=r.index,s=m.listData[v].faces.$front,c=m.listData[v].faces.$back,m.listData[v].isReversed=p,d=m.listData[v].direction,height=m.listData[v].height,width=m.listData[v].width,margin=m.listData[v].margin;if(e.capabilities.canFlip3d&&m.useHardwareAccel){u=p?"360deg":"180deg",h="vertical"===d?"rotateX("+u+")":"rotateY("+u+")",f=o.appendStyleProperties({},["transform","transition"],[h,"all "+m.speed+"ms "+m.haTransFunc+" 0s"]);var E=p?"540deg":"360deg",D="vertical"===d?"rotateX("+E+")":"rotateY("+E+")",I=o.appendStyleProperties({},["transform","transition"],[D,"all "+m.speed+"ms "+m.haTransFunc+" 0s"]);
s.css(f),c.css(I);var w=function(){r.animating=!1;var e,t;p?(e="vertical"===d?"rotateX(0deg)":"rotateY(0deg)",t=o.appendStyleProperties({},["transform","transition"],[e,"all 0s "+m.haTransFunc+" 0s"]),s.css(t),i.runContenModules(m,s,c,v),g?(b(),m.animationComplete.call(l[0],m,s,c)):a(m,s,c),s=null,c=null,m=null,r=null):(i.runContenModules(m,c,s,v),g?(b(),m.animationComplete.call(l[0],m,c,s)):a(m,c,s))};"flip-list"===m.mode?(window.clearTimeout(m.listData[v].completeTimeout),m.listData[v].completeTimeout=window.setTimeout(w,m.speed)):(window.clearTimeout(m.completeTimeout),m.completeTimeout=window.setTimeout(w,m.speed))}else{var S,_=m.speed/2,O="vertical"===d?{height:"0px",width:"100%",marginTop:margin+"px",opacity:m.noHAflipOpacity}:{height:"100%",width:"0px",marginLeft:margin+"px",opacity:m.noHAflipOpacity},k="vertical"===d?{height:"100%",width:"100%",marginTop:"0px",opacity:"1"}:{height:"100%",width:"100%",marginLeft:"0px",opacity:"1"};p?(r.animating=!0,c.stop().animate(O,{duration:_}),S=function(){r.animating=!1,s.stop().animate(k,{duration:_,complete:function(){i.runContenModules(m,s,c,v),g?(b(),m.animationComplete.call(l[0],m,s,c)):a(m,s,c),r=null,s=null,c=null}})},"flip-list"===m.mode?(window.clearTimeout(m.listData[r.index].completeTimeout),m.listData[r.index].completeTimeout=window.setTimeout(S,_)):(window.clearTimeout(m.completeTimeout),m.completeTimeout=window.setTimeout(S,_))):(r.animating=!0,s.stop().animate(O,{duration:_}),S=function(){r.animating=!1,c.stop().animate(k,{duration:_,complete:function(){i.runContenModules(m,c,s,v),g?(b(),m.animationComplete.call(l[0],m,c,s)):a(m,c,s),s=null,c=null,m=null,r=null}})},"flip-list"===m.mode?(window.clearTimeout(m.listData[r.index].completeTimeout),m.listData[r.index].completeTimeout=window.setTimeout(S,_)):(window.clearTimeout(m.completeTimeout),m.completeTimeout=window.setTimeout(S,_)))}},flipList:function(e){var n=e.data("LiveTile"),o=n.speed,a=!1,r=function(){(n.timer.repeatCount>0||-1==n.timer.repeatCount)&&n.timer.count!=n.timer.repeatCount&&n.timer.start(n.delay)};n.timer.pause();var s=n.animationStarting.call(e[0],n,null,null);return"undefined"!=typeof s&&0==s?(r(),void 0):(n.loopCount=n.loopCount+1,n.faces.$listTiles.each(function(e,r){var s=l(r),c=s.data("metrojs.tile"),d=n.triggerDelay(e),u=n.speed+Math.max(d,0),h=n.alwaysTrigger;h||(h=351*Math.random()>150?!0:!1),h&&(a=!0,o=Math.max(u+n.speed,o),window.clearTimeout(c.flCompleteTimeout),c.flCompleteTimeout=window.setTimeout(function(){i.flip(s,c.count,n,function(){c.count++,c.count>=t&&(c.count=1),s=null,c=null})},u))}),a&&(window.clearTimeout(n.flCompleteTimeout),n.flCompleteTimeout=window.setTimeout(function(){i.runContenModules(n,null,null,-1),n.animationComplete.call(e[0],n,null,null),r()},o+n.speed)),void 0)}},o={stylePrefixes:"Webkit Moz O ms Khtml ".split(" "),domPrefixes:"-webkit- -moz- -o- -ms- -khtml- ".split(" "),browserPrefix:null,appendStyleProperties:function(e,t,n){for(var i=0;i<=t.length-1;i++)e[l.trim(this.browserPrefix+t[i])]=n[i],e[l.trim(t[i])]=n[i];return e},applyStyleValue:function(e,t,n){return e[l.trim(this.browserPrefix+t)]=n,e[t]=n,e},getBrowserPrefix:function(){if(null==this.browserPrefix){for(var l="",e=0;e<=this.domPrefixes.length-1;e++)"undefined"!=typeof document.body.style[this.domPrefixes[e]+"transform"]&&(l=this.domPrefixes[e]);return this.browserPrefix=l}return this.browserPrefix},shuffleArray:function(l){for(var e=[];l.length;)e.push(l.splice(Math.random()*l.length,1));for(;e.length;)l.push(e.pop());return l}},a={moduleName:"custom",customSwap:{data:{customDoSwapFront:function(){return!1},customDoSwapBack:function(){return!1},customGetContent:function(){return null}},initData:function(e){var t={};t.doSwapFront=l.inArray("custom",e.swapFront)>-1&&e.customDoSwapFront(),t.doSwapBack=l.inArray("custom",e.swapBack)>-1&&e.customDoSwapBack(),e.customSwap="undefined"!=typeof e.customSwap?l.extend(t,e.customSwap):t},action:function(){}},htmlSwap:{moduleName:"html",data:{frontContent:[],frontIsRandom:!0,frontIsInGrid:!1,backContent:[],backIsRandom:!0,backIsInGrid:!1},initData:function(e,t){var n={backBag:[],backIndex:0,backStaticIndex:0,backStaticRndm:-1,prevBackIndex:-1,frontBag:[],frontIndex:0,frontStaticIndex:0,frontStaticRndm:-1,prevFrontIndex:-1};e.ignoreDataAttributes?(n.frontIsRandom=e.frontIsRandom,n.frontIsInGrid=e.frontIsInGrid,n.backIsRandom=e.backIsRandom,n.backIsInGrid=e.backIsInGrid):(n.frontIsRandom=i.getDataOrDefault(t,"front-israndom",e.frontIsRandom),n.frontIsInGrid=i.getDataOrDefault(t,"front-isingrid",e.frontIsInGrid),n.backIsRandom=i.getDataOrDefault(t,"back-israndom",e.backIsRandom),n.backIsInGrid=i.getDataOrDefault(t,"back-isingrid",e.backIsInGrid)),n.doSwapFront=l.inArray("html",e.swapFront)>-1&&e.frontContent instanceof Array&&e.frontContent.length>0,n.doSwapBack=l.inArray("html",e.swapBack)>-1&&e.backContent instanceof Array&&e.backContent.length>0,e.htmlSwap="undefined"!=typeof e.htmlSwap?l.extend(n,e.htmlSwap):n,e.htmlSwap.doSwapFront&&(e.htmlSwap.frontBag=this.prepBag(e.htmlSwap.frontBag,e.frontContent,e.htmlSwap.prevFrontIndex),e.htmlSwap.frontStaticRndm=e.htmlSwap.frontBag.pop()),e.htmlSwap.doSwapBack&&(e.htmlSwap.backBag=this.prepBag(e.htmlSwap.backBag,e.backContent,e.htmlSwap.prevBackIndex),e.htmlSwap.backStaticRndm=e.htmlSwap.backBag.pop())},prepBag:function(l,e,t){l=l||[];for(var n=0,i=0;i<e.length;i++)(i!=t||1===l.length)&&(l[n]=i,n++);return o.shuffleArray(l)},getFrontSwapIndex:function(l){var e=0;return l.htmlSwap.frontIsRandom?(0===l.htmlSwap.frontBag.length&&(l.htmlSwap.frontBag=this.prepBag(l.htmlSwap.frontBag,l.frontContent,l.htmlSwap.prevFrontIndex)),e=l.htmlSwap.frontIsInGrid?l.htmlSwap.frontStaticRndm:l.htmlSwap.frontBag.pop()):e=l.htmlSwap.frontIsInGrid?l.htmlSwap.frontStaticIndex:l.htmlSwap.frontIndex,e},getBackSwapIndex:function(l){var e=0;return l.htmlSwap.backIsRandom?(0===l.htmlSwap.backBag.length&&(l.htmlSwap.backBag=this.prepBag(l.htmlSwap.backBag,l.backContent,l.htmlSwap.prevBackIndex)),e=l.htmlSwap.backIsInGrid?l.htmlSwap.backStaticRndm:l.htmlSwap.backBag.pop()):e=l.htmlSwap.backIsInGrid?l.htmlSwap.backStaticIndex:l.htmlSwap.backIndex,e},action:function(l,e,t,n){if(l.htmlSwap.doSwapFront||l.htmlSwap.doSwapBack){var i="flip-list"===l.mode,o=0,a=i?l.listData[Math.max(n,0)].isReversed:l.isReversed;if(i&&-1==n)return a?l.htmlSwap.doSwapBack&&(0===l.htmlSwap.backBag.length&&(l.htmlSwap.backBag=this.prepBag(l.htmlSwap.backBag,l.backContent,l.htmlSwap.backStaticRndm)),l.htmlSwap.backStaticRndm=l.htmlSwap.backBag.pop(),l.htmlSwap.backStaticIndex++,l.htmlSwap.backStaticIndex>=l.backContent.length&&(l.htmlSwap.backStaticIndex=0)):l.htmlSwap.doSwapFront&&(0===l.htmlSwap.frontBag.length&&(l.htmlSwap.frontBag=this.prepBag(l.htmlSwap.frontBag,l.frontContent,l.htmlSwap.frontStaticRndm)),l.htmlSwap.frontStaticRndm=l.htmlSwap.frontBag.pop(),l.htmlSwap.frontStaticIndex++,l.htmlSwap.frontStaticIndex>=l.frontContent.length&&(l.htmlSwap.frontStaticIndex=0)),void 0;if(a){if(!l.htmlSwap.doSwapBack)return;o=this.getBackSwapIndex(l),l.htmlSwap.prevBackIndex=o,t.html(l.backContent[l.htmlSwap.backIndex]),l.htmlSwap.backIndex++,l.htmlSwap.backIndex>=l.backContent.length&&(l.htmlSwap.backIndex=0),i||(l.htmlSwap.backStaticIndex++,l.htmlSwap.backStaticIndex>=l.backContent.length&&(l.htmlSwap.backStaticIndex=0))}else{if(!l.htmlSwap.doSwapFront)return;o=this.getFrontSwapIndex(l),l.htmlSwap.prevFrontIndex=o,"slide"===l.mode?l.startNow?t.html(l.frontContent[o]):e.html(l.frontContent[o]):t.html(l.frontContent[o]),l.htmlSwap.frontIndex++,l.htmlSwap.frontIndex>=l.frontContent.length&&(l.htmlSwap.frontIndex=0),i||(l.htmlSwap.frontStaticIndex++,l.htmlSwap.frontStaticIndex>=l.frontContent.length&&(l.htmlSwap.frontStaticIndex=0))}}}},imageSwap:{moduleName:"image",data:{preloadImages:!1,imageCssSelector:">img,>a>img",fadeSwap:!1,frontImages:[],frontIsRandom:!0,frontIsBackgroundImage:!1,frontIsInGrid:!1,backImages:null,backIsRandom:!0,backIsBackgroundImage:!1,backIsInGrid:!1},initData:function(e,t){var n={backBag:[],backIndex:0,backStaticIndex:0,backStaticRndm:-1,frontBag:[],frontIndex:0,frontStaticIndex:0,frontStaticRndm:-1,prevBackIndex:-1,prevFrontIndex:-1},o=e.ignoreDataAttributes;o?(n.imageCssSelector=i.getDataOrDefault(t,"image-css",e.imageCssSelector),n.fadeSwap=i.getDataOrDefault(t,"fadeswap",e.fadeSwap),n.frontIsRandom=i.getDataOrDefault(t,"front-israndom",e.frontIsRandom),n.frontIsInGrid=i.getDataOrDefault(t,"front-isingrid",e.frontIsInGrid),n.frontIsBackgroundImage=i.getDataOrDefault(t,"front-isbg",e.frontIsBackgroundImage),n.backIsRandom=i.getDataOrDefault(t,"back-israndom",e.backIsRandom),n.backIsInGrid=i.getDataOrDefault(t,"back-isingrid",e.backIsInGrid),n.backIsBackgroundImage=i.getDataOrDefault(t,"back-isbg",e.backIsBackgroundImage),n.doSwapFront=l.inArray("image",e.swapFront)>-1&&e.frontImages instanceof Array&&e.frontImages.length>0,n.doSwapBack=l.inArray("image",e.swapBack)>-1&&e.backImages instanceof Array&&e.backImages.length>0,n.alwaysSwapFront=i.getDataOrDefault(t,"front-alwaysswap",e.alwaysSwapFront),n.alwaysSwapBack=i.getDataOrDefault(t,"back-alwaysswap",e.alwaysSwapBack)):(n.imageCssSelector=e.imageCssSelector,n.fadeSwap=e.fadeSwap,n.frontIsRandom=e.frontIsRandom,n.frontIsInGrid=e.frontIsInGrid,n.frontIsBackgroundImage=e.frontIsBackgroundImage,n.backIsRandom=e.backIsRandom,n.backIsInGrid=e.backIsInGrid,n.backIsBackgroundImage=e.backIsBackgroundImage,n.doSwapFront=l.inArray("image",e.swapFront)>-1&&e.frontImages instanceof Array&&e.frontImages.length>0,n.doSwapBack=l.inArray("image",e.swapBack)>-1&&e.backImages instanceof Array&&e.backImages.length>0,n.alwaysSwapFront=e.alwaysSwapFront,n.alwaysSwapBack=e.alwaysSwapBack),e.imgSwap="undefined"!=typeof e.imgSwap?l.extend(n,e.imgSwap):n,e.imgSwap.doSwapFront&&(e.imgSwap.frontBag=this.prepBag(e.imgSwap.frontBag,e.frontImages,e.imgSwap.prevFrontIndex),e.imgSwap.frontStaticRndm=e.imgSwap.frontBag.pop(),e.preloadImages&&l(e.frontImages).metrojs.preloadImages(function(){})),e.imgSwap.doSwapBack&&(e.imgSwap.backBag=this.prepBag(e.imgSwap.backBag,e.backImages,e.imgSwap.prevBackIndex),e.imgSwap.backStaticRndm=e.imgSwap.backBag.pop(),e.preloadImages&&l(e.backImages).metrojs.preloadImages(function(){}))},prepBag:function(l,e,t){l=l||[];for(var n=0,i=0;i<e.length;i++)(i!=t||1===e.length)&&(l[n]=i,n++);return o.shuffleArray(l)},getFrontSwapIndex:function(l){var e=0;return l.imgSwap.frontIsRandom?(0===l.imgSwap.frontBag.length&&(l.imgSwap.frontBag=this.prepBag(l.imgSwap.frontBag,l.frontImages,l.imgSwap.prevFrontIndex)),e=l.imgSwap.frontIsInGrid?l.imgSwap.frontStaticRndm:l.imgSwap.frontBag.pop()):e=l.imgSwap.frontIsInGrid?l.imgSwap.frontStaticIndex:l.imgSwap.frontIndex,e},getBackSwapIndex:function(l){var e=0;return l.imgSwap.backIsRandom?(0===l.imgSwap.backBag.length&&(l.imgSwap.backBag=this.prepBag(l.imgSwap.backBag,l.backImages,l.imgSwap.prevBackIndex)),e=l.imgSwap.backIsInGrid?l.imgSwap.backStaticRndm:l.imgSwap.backBag.pop()):e=l.imgSwap.backIsInGrid?l.imgSwap.backStaticIndex:l.imgSwap.backIndex,e},setImageProperties:function(e,t,n){var i={},o={};"undefined"!=typeof t.src&&(n?i.backgroundImage="url('"+t.src+"')":o.src=t.src),"undefined"!=typeof t.alt&&(o.alt=t.alt),"object"==typeof t.css?e.css(l.extend(i,t.css)):e.css(i),"object"==typeof t.attr?e.attr(l.extend(o,t.attr)):e.attr(o)},action:function(l,e,t,n){if(l.imgSwap.doSwapFront||l.imgSwap.doSwapBack){var i="flip-list"===l.mode,o=("slide"==l.mode,0),r=i?l.listData[Math.max(n,0)].isReversed:l.isReversed;if(i&&-1==n)return(l.alwaysSwapFront||!r)&&l.imgSwap.doSwapFront&&(0===l.imgSwap.frontBag.length&&(l.imgSwap.frontBag=this.prepBag(l.imgSwap.frontBag,l.frontImages,l.imgSwap.frontStaticRndm)),l.imgSwap.frontStaticRndm=l.imgSwap.frontBag.pop(),l.imgSwap.frontStaticIndex++,l.imgSwap.frontStaticIndex>=l.frontImages.length&&(l.imgSwap.frontStaticIndex=0)),(l.alwaysSwapBack||r)&&l.imgSwap.doSwapBack&&(0===l.imgSwap.backBag.length&&(l.imgSwap.backBag=this.prepBag(l.imgSwap.backBag,l.backImages,l.imgSwap.backStaticRndm)),l.imgSwap.backStaticRndm=l.imgSwap.backBag.pop(),l.imgSwap.backStaticIndex++,l.imgSwap.backStaticIndex>=l.backImages.length&&(l.imgSwap.backStaticIndex=0)),void 0;var s,c,d,u;if(l.alwaysSwapFront||!r){if(!l.imgSwap.doSwapFront)return;o=this.getFrontSwapIndex(l),l.imgSwap.prevFrontIndex=o,s="slide"===l.mode?e:t,c=s.find(l.imgSwap.imageCssSelector),d="object"==typeof l.frontImages[o]?l.frontImages[o]:{src:l.frontImages[o]},u=function(e){var t=l.imgSwap.frontIsBackgroundImage;"function"==typeof e&&(t?window.setTimeout(e,100):c[0].onload=e),a.imageSwap.setImageProperties(c,d,t)},l.fadeSwap?c.fadeOut(function(){u(function(){c.fadeIn()})}):u(),l.imgSwap.frontIndex++,l.imgSwap.frontIndex>=l.frontImages.length&&(l.imgSwap.frontIndex=0),i||(l.imgSwap.frontStaticIndex++,l.imgSwap.frontStaticIndex>=l.frontImages.length&&(l.imgSwap.frontStaticIndex=0))}if(l.alwaysSwapBack||r){if(!l.imgSwap.doSwapBack)return;o=this.getBackSwapIndex(l),l.imgSwap.prevBackIndex=o,s=t,c=s.find(l.imgSwap.imageCssSelector),d="object"==typeof l.backImages[o]?l.backImages[o]:{src:l.backImages[o]},u=function(){a.imageSwap.setImageProperties(c,d,l.imgSwap.backIsBackgroundImage)},l.fadeSwap?c.fadeOut(function(){u(function(){c.fadeIn()})}):u(),l.imgSwap.backIndex++,l.imgSwap.backIndex>=l.backImages.length&&(l.imgSwap.backIndex=0),i||(l.imgSwap.backStaticIndex++,l.imgSwap.backStaticIndex>=l.backImages.length&&(l.imgSwap.backStaticIndex=0))}}}}};l.fn.metrojs.TileTimer=function(l,e,n){this.timerId=null,this.interval=l,this.action=e,this.count=0,this.repeatCount="undefined"==typeof n?0:n,this.start=function(e){window.clearTimeout(this.timerId);var t=this;this.timerId=window.setTimeout(function(){t.tick.call(t,l)},e)},this.tick=function(l){this.action(this.count+1),this.count++,this.count>=t&&(this.count=0),(this.repeatCount>0||-1==this.repeatCount)&&(this.count!=this.repeatCount?this.start(l):this.stop())},this.stop=function(){this.timerId=window.clearTimeout(this.timerId),this.reset()},this.resume=function(){(this.repeatCount>0||-1==this.repeatCount)&&this.count!=this.repeatCount&&this.start(l)},this.pause=function(){this.timerId=window.clearTimeout(this.timerId)},this.reset=function(){this.count=0},this.restart=function(l){this.stop(),this.start(l)}},jQuery.fn.metrojs.theme={loadDefaultTheme:function(l){if("undefined"==typeof l||null==l)l=jQuery.fn.metrojs.theme.defaults;else{var e=jQuery.fn.metrojs.theme.defaults;jQuery.extend(e,l),l=e}var t="undefined"!=typeof window.localStorage,n=function(l){return"undefined"!=typeof window.localStorage[l]&&null!=window.localStorage[l]};!t||n("Metro.JS.AccentColor")&&n("Metro.JS.BaseAccentColor")?t?(l.accentColor=window.localStorage["Metro.JS.AccentColor"],l.baseTheme=window.localStorage["Metro.JS.BaseAccentColor"],jQuery(l.accentCssSelector).addClass(l.accentColor).data("accent",l.accentColor),jQuery(l.baseThemeCssSelector).addClass(l.baseTheme),"function"==typeof l.loaded&&l.loaded(l.baseTheme,l.accentColor)):(jQuery(l.accentCssSelector).addClass(l.accentColor).data("accent",l.accentColor),jQuery(l.baseThemeCssSelector).addClass(l.baseTheme),"function"==typeof l.loaded&&l.loaded(l.baseTheme,l.accentColor),"undefined"!=typeof l.preloadAltBaseTheme&&l.preloadAltBaseTheme&&jQuery(["dark"==l.baseTheme?l.metroLightUrl:l.metroDarkUrl]).metrojs.preloadImages(function(){})):(window.localStorage["Metro.JS.AccentColor"]=l.accentColor,window.localStorage["Metro.JS.BaseAccentColor"]=l.baseTheme,jQuery(l.accentCssSelector).addClass(l.accentColor).data("accent",l.accentColor),jQuery(l.baseThemeCssSelector).addClass(l.baseTheme),"function"==typeof l.loaded&&l.loaded(l.baseTheme,l.accentColor),"undefined"!=typeof l.preloadAltBaseTheme&&l.preloadAltBaseTheme&&jQuery(["dark"==l.baseTheme?l.metroLightUrl:l.metroDarkUrl]).metrojs.preloadImages(function(){}))},applyTheme:function(l,e,t){if("undefined"==typeof t||null==t)t=jQuery.fn.metrojs.theme.defaults;else{var n=jQuery.fn.metrojs.theme.defaults;t=jQuery.extend({},n,t)}if("undefined"!=typeof l&&null!=l){"undefined"!=typeof window.localStorage&&(window.localStorage["Metro.JS.BaseAccentColor"]=l);var i=jQuery(t.baseThemeCssSelector);i.length>0&&("dark"==l?i.addClass("dark").removeClass("light"):"light"==l&&i.addClass("light").removeClass("dark"))}if("undefined"!=typeof e&&null!=e){"undefined"!=typeof window.localStorage&&(window.localStorage["Metro.JS.AccentColor"]=e);var o=jQuery(t.accentCssSelector);if(o.length>0){var a=!1;o.each(function(){jQuery(this).addClass(e);var l=jQuery(this).data("accent");if(l!=e){var t=jQuery(this).attr("class").replace(l,"");t=t.replace(/(\s)+/," "),jQuery(this).attr("class",t),jQuery(this).data("accent",e),a=!0}}),a&&"function"==typeof t.accentPicked&&t.accentPicked(e)}}},appendAccentColors:function(e){if("undefined"==typeof e||null==e)e=jQuery.fn.metrojs.theme.defaults;else{var t=jQuery.fn.metrojs.theme.defaults;e=jQuery.extend({},t,e)}for(var n="",i=e.accentColors,o=e.accentListTemplate,a=0;a<i.length;a++)n+=o.replace(/\{0\}/g,i[a]);l(n).appendTo(e.accentListContainer)},appendBaseThemes:function(e){if("undefined"==typeof e||null==e)e=jQuery.fn.metrojs.theme.defaults;else{var t=jQuery.fn.metrojs.theme.defaults;e=jQuery.extend({},t,e)}for(var n="",i=e.baseThemes,o=e.baseThemeListTemplate,a=0;a<i.length;a++)n+=o.replace(/\{0\}/g,i[a]);l(n).appendTo(e.baseThemeListContainer)},defaults:{baseThemeCssSelector:"body",accentCssSelector:".tiles",accentColor:"blue",baseTheme:"dark",accentColors:["amber","blue","brown","cobalt","crimson","cyan","magenta","lime","indigo","green","emerald","mango","mauve","olive","orange","pink","red","sienna","steel","teal","violet","yellow"],baseThemes:["light","dark"],accentListTemplate:"<li><a href='javascript:;' title='{0}' class='accent {0}'></a></li>",accentListContainer:"ul.theme-options,.theme-options>ul",baseThemeListTemplate:"<li><a href='javascript:;' title='{0}' class='accent {0}'></a></li>",baseThemeListContainer:"ul.base-theme-options,.base-theme-options>ul"}},jQuery.fn.applicationBar=function(e){var t="undefined"!=typeof jQuery.fn.metrojs.theme?jQuery.fn.metrojs.theme.defaults:{};if(jQuery.extend(t,jQuery.fn.applicationBar.defaults,e),"undefined"!=typeof jQuery.fn.metrojs.theme){var n=jQuery.fn.metrojs.theme;t.shouldApplyTheme&&n.loadDefaultTheme(t);var i=t.accentListContainer.replace(","," a,")+" a",o=function(){var l=jQuery(this).attr("class").replace("accent","").replace(" ","");n.applyTheme(null,l,t),"function"==typeof t.accentPicked&&t.accentPicked(l)},a=t.baseThemeListContainer.replace(","," a,")+" a",r=function(){var l=jQuery(this).attr("class").replace("accent","").replace(" ","");n.applyTheme(l,null,t),"function"==typeof t.themePicked&&t.themePicked(l)};"function"==typeof l.fn.on?(l(this).on("click.appBar",i,o),l(this).on("click.appBar",a,r)):(l(i).live("click.appBar",o),l(a).live("click.appBar",r))}return l(this).each(function(e,n){var i=l(n),o=l.extend({},t);"auto"==o.collapseHeight&&(o.collapseHeight=l(this).height()),navigator.userAgent.match(/(Android|webOS|iPhone|iPod|BlackBerry|PIE|IEMobile)/i)&&(navigator.userAgent.match(/(IEMobile\/1)/i)||navigator.userAgent.match(/(iPhone OS [56789])/i)||i.css({position:"absolute",bottom:"0px"})),o.slideOpen=function(){i.hasClass("expanded")||o.animateAppBar(!1)},o.slideClosed=function(){i.hasClass("expanded")&&o.animateAppBar(!0)},o.animateAppBar=function(l){var e=l?o.collapseHeight:o.expandHeight;l?i.removeClass("expanded"):i.hasClass("expanded")||i.addClass("expanded"),i.stop().animate({height:e},{duration:o.duration})},i.data("ApplicationBar",o),i.find(t.handleSelector).click(function(){o.animateAppBar(i.hasClass("expanded"))}),1==o.bindKeyboard&&jQuery(document.documentElement).keyup(function(l){38==l.keyCode?l.target&&null==l.target.tagName.match(/INPUT|TEXTAREA|SELECT/i)&&(i.hasClass("expanded")||o.animateAppBar(!1)):40==l.keyCode&&l.target&&null==l.target.tagName.match(/INPUT|TEXTAREA|SELECT/i)&&i.hasClass("expanded")&&o.animateAppBar(!0)})})},jQuery.fn.applicationBar.defaults={applyTheme:!0,themePicked:function(){},accentPicked:function(){},loaded:function(){},duration:300,expandHeight:"320px",collapseHeight:"auto",bindKeyboard:!0,handleSelector:"a.etc",metroLightUrl:"images/metroIcons_light.jpg",metroDarkUrl:"images/metroIcons.jpg",preloadAltBaseTheme:!1},l.fn.metrojs.preloadImages=function(e){var t=l(this).toArray(),n=l("<img style='display:none;' />").appendTo("body");l(this).each(function(){var l=this;"object"==typeof this&&(l=this.src),n.attr({src:l}).load(function(){for(var l=0;l<t.length;l++)t[l]==element&&t.splice(l,1);0==t.length&&e()})}),n.remove()},l.fn.metrojs.MetroModernizr=function(e){if("undefined"==typeof e&&(e={useHardwareAccel:!0,useModernizr:"undefined"!=typeof window.Modernizr}),this.isOldJQuery=/^1\.[0123]/.test(l.fn.jquery),this.isOldAndroid=function(){try{var e=navigator.userAgent;if(e.indexOf("Android")>=0){var t=parseFloat(e.slice(e.indexOf("Android")+8));if(2.3>t)return!0}}catch(n){l.error(n)}return!1}(),this.canTransform=!1,this.canTransition=!1,this.canTransform3d=!1,this.canAnimate=!1,this.canTouch=!1,this.canFlip3d=e.useHardwareAccel,1==e.useHardwareAccel)if(0==e.useModernizr)if("undefined"!=typeof window.MetroModernizr)this.canTransform=window.MetroModernizr.canTransform,this.canTransition=window.MetroModernizr.canTransition,this.canTransform3d=window.MetroModernizr.canTransform3d,this.canAnimate=window.MetroModernizr.canAnimate,this.canTouch=window.MetroModernizr.canTouch;else{window.MetroModernizr={};var t="metromodernizr",n=document.documentElement,i=document.head||document.getElementsByTagName("head")[0],o=document.createElement(t),a=o.style,r=" -webkit- -moz- -o- -ms- ".split(" "),s="Webkit Moz O ms Khtml".split(" "),c=function(l,e){for(var t in l)if(void 0!==a[l[t]]&&(!e||e(l[t],o)))return!0},d=function(l,e){var t=l.charAt(0).toUpperCase()+l.substr(1),n=(l+" "+s.join(t+" ")+t).split(" ");return!!c(n,e)},u=function(){var l=!!c(["perspectiveProperty","WebkitPerspective","MozPerspective","OPerspective","msPerspective"]);return l&&"webkitPerspective"in n.style&&(l=h(["@media (",r.join("transform-3d),("),t,")","{#metromodernizr{left:9px;position:absolute;height:3px;}}"].join(""),function(l){return 3===l.offsetHeight&&9===l.offsetLeft})),l},h=function(l,e){var o,a=document.createElement("style"),r=document.createElement("div");return a.textContent=l,i.appendChild(a),r.id=t,n.appendChild(r),o=e(r),a.parentNode.removeChild(a),r.parentNode.removeChild(r),!!o},f=function(){return canTouch="ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch||"undefined"!=typeof window.navigator.msMaxTouchPoints&&window.navigator.msMaxTouchPoints>0||h(["@media (",r.join("touch-enabled),("),t,")","{#metromodernizr{top:9px;position:absolute}}"].join(""),function(l){return 9===l.offsetTop})};this.canTransform=!!c(["transformProperty","WebkitTransform","MozTransform","OTransform","msTransform"]),this.canTransition=d("transitionProperty"),this.canTransform3d=u(),this.canAnimate=d("animationName"),this.canTouch=f(),window.MetroModernizr.canTransform=this.canTransform,window.MetroModernizr.canTransition=this.canTransition,window.MetroModernizr.canTransform3d=this.canTransform3d,window.MetroModernizr.canAnimate=this.canAnimate,window.MetroModernizr.canTouch=this.canTouch,n=null,i=null,o=null,a=null}else this.canTransform=l("html").hasClass("csstransforms"),this.canTransition=l("html").hasClass("csstransitions"),this.canTransform3d=l("html").hasClass("csstransforms3d"),this.canAnimate=l("html").hasClass("cssanimations"),this.canTouch=l("html").hasClass("touch")||"undefined"!=typeof window.navigator.msMaxTouchPoints&&window.navigator.msMaxTouchPoints>0;this.canFlip3d=this.canFlip3d&&this.canAnimate&&this.canTransform&&this.canTransform3d}}(jQuery);