
/*
 |--------------------------------------------------------------------------
 | <PERSON>vel Spark Components
 |--------------------------------------------------------------------------
 |
 | Here we will load the Spark components which makes up the core client
 | application. This is also a convenient spot for you to load all of
 | your components that you write while building your applications.
 */

require('./../spark-components/bootstrap');

require('./home');

require('./ui/select2-multiple');
require('./ui/datatable');
require('./ui/datatable-ajax');
require('./ui/widgets/stats-counter');
require('./ui/time-of-day');
require('./ui/huron-html5-editor');
require('./ui/select-timezone');
// require('./ui/email-activity-old'); // Replaced
require('./ui/email-activity');
require('./ui/prospect-activity');
require('./ui/campaign-activity');
require('./ui/style-guide');
require('./ui/expand');
require('./ui/toggle-switch.vue');

require('./auth/register-agency');
require('./settings/profile/update-phone-number');
require('./settings/agency');
require('./settings/agency/update-agency-information');
require('./settings/agency/update-agency-logo');
require('./settings/user-team/user-team');

require('./settings/customers-invitations');
require('./settings/customers-invitations/mailed-customers-invitations');
require('./settings/customers-invitations/send-customers-invitations');
require('./settings/customers-invitations/customers-invitations-signature');

require('./settings/zap-api_key');
require('./settings/chatgpt-key');
require('./settings/email-search-settings');

require('./campaigns/campaign-view');

require('./stats/stats-campaign');
require('./stats/stats-report');
require('./stats/stats-date-range-report');
require('./stats/linkedin-search-date-range-report');

require('./prospects/prospect-search');
require('./prospects/prospect-list');

require('./agency/agency-setup');
require('./agency/agency-admins');
require('./agency/agency-billing');
require('./agency/agency-upgrade');
require('./agency/agency-activate');
require('./agency/wavo3-activate');
require('./agency/wavo3-renew');
require('./agency/chat-settings');

require('./clients/clients-index');
require('./clients/client-profile');
require('./clients/update-client-information');

require('./sequences/StageList');
require('./sequences/EmailStage');
require('./sequences/LinkedinMessageStage');
require('./sequences/ManualTaskStage');
require('./sequences/preview-templates/PreviewTemplateSlideout');
require('./sequences/stage-templates/EmailTemplateSlideout.vue');
require('./sequences/stage-templates/TemplateSchedule.vue');
require('./sequences/preview-templates/InlineMergefield');

require('./chatgpt/ChatgptPromptList');
require('./chatgpt/PromptSlideout');
require('./chatgpt/PromptListItem');
require('./chatgpt/PromptTest');
require('./chatgpt/PromptTemplate');

require('./inboxes/thread-list');
require('./inboxes/campaign-threads');
require('./inboxes/inbox-list');
require('./inboxes/email-edit');
require('./inboxes/email-add');
require('./inboxes/email-delete');
require('./inboxes/email-reauth');
require('./inboxes/incoming-email-list');
require('./inboxes/mailreach-warmup');
require('./inboxes/warmup-inbox');

require('./campaigns/campaign-list');
require('./campaigns/campaign-edit');
require('./campaigns/campaign-add');
require('./campaigns/campaign-delete');

require('./clients/client-list');
require('./clients/client-show');
require('./clients/client-edit');
require('./clients/client-add');
require('./clients/client-existing');

require('./research/research-project-list');
require('./research/ResearchProject.vue');

require('./users/user-list');

require('./subdomainblocks/subdomain-block-list');
require('./domains/domain-list');
require('./emailblocks/emailblock-list');
require('./ui/pagination/pagination');

require('./ui/tiny-config');
require('./ui/tiny-editor');
require('./ui/tinyPluginMergeField');
require('./ui/tinyPluginVariableField');
require('./test/editor-tiny');
require('./test/test-component');
require('./test/test-component2');
require('./test/test-component3');

require('./admin/management-report');
require('./admin/platform-host-manager');

require('./linkedin-searches/search-list');
require('./linkedin-searches/search-show');
require('./linkedin-searches/search-add');
require('./linkedin-searches/search-activity');
require('./linkedin-accounts/account-list');
require('./linkedin-accounts/account-add');
require('./linkedin-accounts/account-edit');
require('./linkedin-accounts/account-show');
require('./linkedin-accounts/account-reauth');
require('./linkedin-accounts/account-activity');
require('./linkedin-subscriptions/linkedin-subscription-create');
require('./linkedin-access/access-request');

require('./billing/CheckoutPlf');
require('./billing/CheckoutWavo3eStartup');
require('./billing/CheckoutWavo3eFree');

require('./proxies/proxy-list');

require('./data-search/search');
require('./data-search/DataItemSlideout');
require('./data-search/emails');
require('./data-search/billing');
require('./data-search/companies');
// require('./data-search/contacts');
require('./data-search/SaveSearchSlideout');
require('./data-search/ContactImportSlideout');
// require('./data-search/DataSearchesSlideout');
// require('./data-search/lists/lists');
require('./data-search/lists/AddToListSlideout');
require('./data-search/lists/ListCreateSlideout');
require('./data-search/lists/ListEditSlideout');
require('./data-search/lists/ListAllSlideout');
// require('./data-search/filters/filters');
require('./data-search/filters/FilterEditSlideout');
require('./data-search/filters/FilterListSlideout');
