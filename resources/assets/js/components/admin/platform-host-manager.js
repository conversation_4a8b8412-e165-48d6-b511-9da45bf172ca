Vue.component('platform-host-manager', {
    props: {
        initialPlatformHosts: Array,
    },

    data: function () {
        return {
            platformHosts: [],
            loading: false,
            loadingMore: false,
            hasMore: true
        }
    },

    mounted() {
        this.platformHosts = this.initialPlatformHosts.map(host => ({
            ...host,
            updating: false
        }));
        
        // Check if we have more data to load
        this.hasMore = this.platformHosts.length === 20;
    },

    methods: {
        updatePlatformStatus(platformHost, isPlatform) {
            platformHost.updating = true;
            
            axios.post('/admin/platform-hosts/update-status', {
                registrable: platformHost.registrable,
                is_platform: isPlatform
            })
            .then(response => {
                if (response.data.success) {
                    // Remove the host from the list since it's no longer null
                    const index = this.platformHosts.findIndex(h => h.registrable === platformHost.registrable);
                    if (index !== -1) {
                        this.platformHosts.splice(index, 1);
                    }
                    
                    // Show success message
                    swal({
                        title: 'Success',
                        text: response.data.message || 'Platform status updated successfully',
                        icon: 'success'
                    });
                } else {
                    swal({
                        title: 'Error',
                        text: 'Failed to update platform status',
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                console.error('Error updating platform status:', error);
                swal({
                    title: 'Error',
                    text: 'An error occurred while updating platform status',
                    icon: 'error'
                });
            })
            .finally(() => {
                platformHost.updating = false;
            });
        },
        
        loadMoreHosts() {
            this.loadingMore = true;
            
            axios.post('/admin/platform-hosts/load-more', {
                offset: this.platformHosts.length
            })
            .then(response => {
                if (response.data.success) {
                    const newHosts = response.data.data.map(host => ({
                        ...host,
                        updating: false
                    }));
                    
                    this.platformHosts.push(...newHosts);
                    this.hasMore = response.data.has_more;
                } else {
                    swal({
                        title: 'Error',
                        text: 'Failed to load more hosts',
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                console.error('Error loading more hosts:', error);
                swal({
                    title: 'Error',
                    text: 'An error occurred while loading more hosts',
                    icon: 'error'
                });
            })
            .finally(() => {
                this.loadingMore = false;
            });
        }
    }
});
