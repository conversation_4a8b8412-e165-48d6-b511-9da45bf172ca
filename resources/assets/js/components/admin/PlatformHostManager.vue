<template>
    <div class="platform-host-manager">
        <div class="page-content">
            <h3>Platform Host Management</h3>
            <p class="text-muted">Review and classify platform hosts. Each load shows 20 hosts with null is_platform status, ordered by subdomain count.</p>

            <div v-if="loading" class="text-center">
                <i class="fa fa-spinner fa-spin"></i> Loading...
            </div>

            <div v-else>
                <div class="panel" v-for="platformHost in platformHosts" :key="platformHost.id">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4>{{ platformHost.registrable }}</h4>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Subdomains:</strong> {{ platformHost.count_subdomains }}
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Confidence:</strong> {{ platformHost.confidence }}
                                    </div>
                                </div>

                                <div class="mt-3" v-if="platformHost.related_domains && platformHost.related_domains.length > 0">
                                    <h5>Related Domains:</h5>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Domain Name</th>
                                                    <th>Merchant Name</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="domain in platformHost.related_domains" :key="domain.id">
                                                    <td>{{ domain.name }}</td>
                                                    <td>{{ domain.merchant_name || 'N/A' }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div v-else class="mt-3">
                                    <p class="text-muted">No related domains found</p>
                                </div>
                            </div>

                            <div class="col-md-4 text-right">
                                <div class="btn-group-vertical" role="group">
                                    <button
                                        class="btn btn-success"
                                        :disabled="platformHost.updating"
                                        @click="updatePlatformStatus(platformHost, true)"
                                    >
                                        <i class="fa fa-check"></i> Mark as Platform
                                    </button>
                                    <button
                                        class="btn btn-danger"
                                        :disabled="platformHost.updating"
                                        @click="updatePlatformStatus(platformHost, false)"
                                    >
                                        <i class="fa fa-times"></i> Mark as Non-Platform
                                    </button>
                                </div>

                                <div v-if="platformHost.updating" class="mt-2">
                                    <i class="fa fa-spinner fa-spin"></i> Updating...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="platformHosts.length === 0" class="text-center">
                    <p class="text-muted">No platform hosts found with null is_platform status.</p>
                </div>

                <div class="text-center mt-4" v-if="hasMore">
                    <button
                        class="btn btn-primary"
                        :disabled="loadingMore"
                        @click="loadMoreHosts"
                    >
                        <i class="fa fa-spinner fa-spin" v-if="loadingMore"></i>
                        <i class="fa fa-plus" v-else></i>
                        Load More
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PlatformHostManager',

    props: {
        initialPlatformHosts: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            platformHosts: [],
            loading: false,
            loadingMore: false,
            hasMore: true
        }
    },

    mounted() {
        this.platformHosts = this.initialPlatformHosts.map(host => ({
            ...host,
            updating: false
        }));

        // Check if we have more data to load
        this.hasMore = this.platformHosts.length === 20;
    },

    methods: {
        updatePlatformStatus(platformHost, isPlatform) {
            platformHost.updating = true;

            axios.post('/admin/platform-hosts/update-status', {
                id: platformHost.id,
                is_platform: isPlatform
            })
            .then(response => {
                if (response.data.success) {
                    // Remove the host from the list since it's no longer null
                    const index = this.platformHosts.findIndex(h => h.id === platformHost.id);
                    if (index !== -1) {
                        this.platformHosts.splice(index, 1);
                    }

                    // Show success message
                    swal({
                        title: 'Success',
                        text: response.data.message || 'Platform status updated successfully',
                        icon: 'success'
                    });
                } else {
                    swal({
                        title: 'Error',
                        text: 'Failed to update platform status',
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                console.error('Error updating platform status:', error);
                swal({
                    title: 'Error',
                    text: 'An error occurred while updating platform status',
                    icon: 'error'
                });
            })
            .finally(() => {
                platformHost.updating = false;
            });
        },

        loadMoreHosts() {
            this.loadingMore = true;

            axios.post('/admin/platform-hosts/load-more', {
                offset: this.platformHosts.length
            })
            .then(response => {
                if (response.data.success) {
                    const newHosts = response.data.data.map(host => ({
                        ...host,
                        updating: false
                    }));

                    this.platformHosts.push(...newHosts);
                    this.hasMore = response.data.has_more;
                } else {
                    swal({
                        title: 'Error',
                        text: 'Failed to load more hosts',
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                console.error('Error loading more hosts:', error);
                swal({
                    title: 'Error',
                    text: 'An error occurred while loading more hosts',
                    icon: 'error'
                });
            })
            .finally(() => {
                this.loadingMore = false;
            });
        }
    }
}
</script>

<style scoped>
.platform-host-manager .panel {
    margin-bottom: 20px;
}

.btn-group-vertical .btn {
    margin-bottom: 5px;
}

.mt-3 {
    margin-top: 1rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-4 {
    margin-top: 1.5rem;
}
</style>
