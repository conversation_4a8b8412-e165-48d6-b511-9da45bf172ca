<div class="site-menubar site-menubar-light">
    <div class="site-menubar-body">
        <div>
            <div>
                <ul class="site-menu" data-plugin="menu">

                    <li class="site-menu-item @if(Request::is('home', 'home/*')) active @endif">
                        <a class="animsition-link" href="/home">
                            <i class="site-menu-icon fa-tachometer" aria-hidden="true"></i>
                            <span class="site-menu-title">Dashboard</span>
                        </a>
                    </li>

                    @if (Auth::user()->agency->isAgencyDashboard())
                      @if (Auth::user()->can('agency-admin') || Auth::user()->can('support'))
                        {{-- agency-dashboard and agency-admin (not agency clients) --}}
                        <li class="site-menu-item @if(Request::is('clients', 'clients/*')) active @endif">
                          <a class="animsition-link" href="/clients">
                            <i class="site-menu-icon wb-users" aria-hidden="true"></i>
                            <span class="site-menu-title">Clients</span>
                          </a>
                        </li>
                      @else
                        <li class="site-menu-item @if(Request::is('clients', 'clients/*')) active @endif">
                          <a class="animsition-link" href="/clients">
                            <i class="site-menu-icon wb-users" aria-hidden="true"></i>
                            <span class="site-menu-title">Team</span>
                          </a>
                        </li>
                      @endif
                    @endif

                    @if ((Auth::user()->agency->wavo_version == 3 && Auth::user()->agency->has_ecommerce) || Auth::user()->can('support'))
                      <li class="site-menu-item @if(Request::is('search', 'search/*')) active @endif">
                        <a class="animsition-link" href="/search">
                          <i class="site-menu-icon fa-search" aria-hidden="true"></i>
                          <span class="site-menu-title">Lead Search</span>
                        </a>
                      </li>
                    @endif

                    <li class="site-menu-item @if(Request::is('campaigns', 'campaigns/*')) active @endif">
                        <a class="animsition-link" href="/campaigns">
                            <i class="site-menu-icon fa-bullhorn" aria-hidden="true"></i>
                            <span class="site-menu-title">Campaigns</span>
                        </a>
                    </li>

                    <li class="site-menu-item @if(Request::is('contacts', 'contacts/*')) active @endif">
                      <a class="animsition-link" href="/contacts">
                        <i class="site-menu-icon fa-address-card-o" aria-hidden="true"></i>
                        <span class="site-menu-title">Campaign Contacts</span>
                      </a>
                    </li>

                    <li class="site-menu-item @if(Request::is('email-accounts', 'email-accounts/*')) active @endif">
                        <a class="animsition-link" href="/email-accounts">
                            <i class="site-menu-icon fa-envelope-o" aria-hidden="true"></i>
                            <span class="site-menu-title">Email Accounts</span>
                        </a>
                    </li>

                    @if (config('app.linkedinEnable'))
                        @if (
                            (Auth::user()->agency->is_linkedin_enabled && Auth::user()->can('agency-admin')) ||
                            (Auth::user()->can('linkedinsearch.read') && !is_null(Auth::user()->agency->owner->subscription('linkedin-data'))) ||
                            (Auth::user()->can('linkedinsearch.read') && Auth::user()->agency->wavo_version == 3 && Auth::user()->agency->is_linkedin_enabled) ||
                            Auth::user()->can('support')
                        )
                            <li class="site-menu-item @if(Request::is('linkedin-accounts', 'linkedin-accounts/*')) active @endif">
                                <a class="animsition-link" href="/linkedin-accounts">
                                    <i class="site-menu-icon fa-linkedin-square" aria-hidden="true"></i>
                                    <span class="site-menu-title">LinkedIn Accounts</span>
                                </a>
                            </li>
                            <li class="site-menu-item @if(Request::is('linkedin-searches', 'linkedin-searches/*')) active @endif">
                                <a class="animsition-link" href="/linkedin-searches">
                                    <i class="site-menu-icon fa-search" aria-hidden="true"></i>
                                    <span class="site-menu-title">LinkedIn Searches</span>
                                </a>
                            </li>
                        @else
                            @if(Auth::user()->can('agency-admin') && !Auth::user()->agency->has_ecommerce)
                              <li class="site-menu-item @if(Request::is('linkedin', 'linkedin/*')) active @endif">
                                  <a class="animsition-link" href="/linkedin-access-request/create">
                                      <i class="site-menu-icon fa-linkedin-square" aria-hidden="true"></i>
                                      <span class="site-menu-title">Linkedin</span>
                                  </a>
                              </li>
                            @endif
                        @endif
                    @endif

                    @if(Auth::user()->agency->isUserDashboard() && Auth::user()->agency->wavo_version == 3)
                    <li class="site-menu-item @if(Request::is('suppression-list')) active @endif">
                      <a class="animsition-link"
                         href="{{ route('suppression-list.show') }}"
                      >
                        <i class="site-menu-icon fa-user-times" aria-hidden="true"></i>
                        <span class="site-menu-title">Suppression List</span>
                      </a>
                    </li>
                    @elseif(Auth::user()->agency->isUserDashboard())
                        <li class="site-menu-item @if(Request::is('team', 'team/*')) active @endif">
                            <a class="animsition-link"
                                href="{{ route('clients.suppressions.index', Auth::user()->currentTeam()) }}"
                            >
                                <i class="site-menu-icon fa-user-times" aria-hidden="true"></i>
                                <span class="site-menu-title">Suppression</span>
                            </a>
                        </li>
                    @endif

                    <li class="dropdown site-menu-item has-sub @if(Request::is('reports', 'reports/*')) active @endif">
                        <a data-toggle="dropdown" href="javascript:void(0)" data-dropdown-toggle="false">
                            <i class="site-menu-icon fa-line-chart" aria-hidden="true"></i>
                            <span class="site-menu-title">Reports</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <div class="dropdown-menu">
                            <div class="site-menu-scroll-wrap is-list">
                                <div>
                                    <div>
                                        <ul class="site-menu-sub site-menu-normal-list">
                                            <li class="site-menu-item">
                                                <a class="animsition-link" href="/reports/campaign-aggregates">
                                                    <span class="site-menu-title">
                                                        <i class="site-menu-icon fa-align-left"></i>
                                                        Aggregate Report
                                                    </span>
                                                </a>
                                            </li>
                                            <li class="site-menu-item">
                                                <a class="animsition-link" href="/reports/date-range">
                                                    <span class="site-menu-title">
                                                        <i class="site-menu-icon fa-calendar"></i>
                                                        Date Range Report
                                                    </span>
                                                </a>
                                            </li>
                                            @can('support')
                                            <li class="site-menu-item">
                                                <a class="animsition-link" href="/reports/linkedin-search-date-range">
                                                    <span class="site-menu-title">
                                                        <i class="site-menu-icon fa-calendar"></i>
                                                        Linkedin Search Report
                                                    </span>
                                                </a>
                                            </li>
                                            @endcan
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    @if (config('app.researchEnable') && Auth::user()->can('research'))
                        <li class="site-menu-item @if(Request::is('research-projects', 'research-projects/*')) active @endif">
                            <a class="animsition-link" href="/research-projects">
                                <i class="site-menu-icon fa-binoculars" aria-hidden="true"></i>
                                <span class="site-menu-title">Lead Research</span>
                            </a>
                        </li>
                    @endif

                    @if (
                        Auth::user()->agency->is_leadsoft_enabled &&
                        (Auth::user()->can('agency-admin') || Auth::user()->can('campaign.admin'))
                    )
                        <li class="site-menu-item @if(Request::is('contact-search', 'contact-search/*')) active @endif">
                            <a class="animsition-link" href="/contact-search">
                                <i class="site-menu-icon fa-search" aria-hidden="true"></i>
                                <span class="site-menu-title">Contact Search</span>
                            </a>
                        </li>
                    @endif

                    <li class="site-menu-item">
                        <a class="animsition-link" href="/settings">
                            <i class="site-menu-icon fa-cogs" aria-hidden="true"></i>
                            <span class="site-menu-title">Settings</span>
                        </a>
                    </li>

                    @can('support')
                        <!-- Admin -->
                        <li class="dropdown site-menu-item has-sub @if(Request::is('admin', 'admin/*')) active @endif">
                            <a data-toggle="dropdown" href="javascript:void(0)" data-dropdown-toggle="false">
                                <i class="site-menu-icon wb-settings" aria-hidden="true"></i>
                                <span class="site-menu-title">Admin - V1</span>
                                <span class="site-menu-arrow"></span>
                            </a>
                            <div class="dropdown-menu">
                                <div class="site-menu-scroll-wrap is-list">
                                    <div>
                                        <div>
                                            <ul class="site-menu-sub site-menu-normal-list">
                                                <li class="site-menu-item">
                                                    <a class="animsition-link" href="/admin/users">
                                                        <span class="site-menu-title">Users</span>
                                                    </a>
                                                </li>
                                                <li class="site-menu-item">
                                                    <a class="animsition-link" href="/admin/wavo3e-user-registration/create">
                                                        <span class="site-menu-title">Wavo 3e User Registration</span>
                                                    </a>
                                                </li>
{{--                                                <li class="site-menu-item">--}}
{{--                                                    <a class="animsition-link" href="/admin/wavo2-user-registration/create">--}}
{{--                                                        <span class="site-menu-title">Wavo 2.0 User Registration</span>--}}
{{--                                                    </a>--}}
{{--                                                </li>--}}
                                                <li class="site-menu-item">
                                                    <a class="animsition-link" href="/admin/agencies">
                                                        <span class="site-menu-title">Agencies</span>
                                                    </a>
                                                </li>
                                                @can('admin')
                                                    <li class="site-menu-item">
                                                        <a class="animsition-link" href="/admin/roles">
                                                            <span class="site-menu-title">Roles</span>
                                                        </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                        <a class="animsition-link" href="/admin/permissions">
                                                            <span class="site-menu-title">Permissions</span>
                                                        </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                        <a class="animsition-link" href="/domains">
                                                            <span class="site-menu-title">Unsubscribe URLs</span>
                                                        </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                        <a class="animsition-link" href="/subdomain-blocks">
                                                            <span class="site-menu-title">Subdomain Block List</span>
                                                        </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/failed-email-messages">
                                                        <span class="site-menu-title">Failed Email Messages</span>
                                                      </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/email-message-tests">
                                                        <span class="site-menu-title">Email Message Tests Log</span>
                                                      </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/email-notifications">
                                                        <span class="site-menu-title">Email Notifications Log</span>
                                                      </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/journeys">
                                                        <span class="site-menu-title">Journeys</span>
                                                      </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/journey-steps">
                                                        <span class="site-menu-title">Journey Steps</span>
                                                      </a>
                                                    </li>
                                                @endcan
                                                <li class="site-menu-item">
                                                  <a class="animsition-link" href="/admin/csv-upload">
                                                    <span class="site-menu-title">CSV Parser</span>
                                                  </a>
                                                </li>
                                                <li class="site-menu-item">
                                                  <a class="animsition-link" href="/admin/search-stats">
                                                    <span class="site-menu-title">Lead Search Stats</span>
                                                  </a>
                                                </li>
                                                <li class="site-menu-item">
                                                  <a class="animsition-link" href="/admin/api-usage">
                                                    <span class="site-menu-title">API Usage Logs</span>
                                                  </a>
                                                </li>
                                                <li class="site-menu-item">
                                                  <a class="animsition-link" href="/admin/domain-clusters">
                                                    <span class="site-menu-title">Domain Clusters</span>
                                                  </a>
                                                </li>
                                                <li class="site-menu-item">
                                                    <a class="animsition-link" href="/admin/platform-hosts">
                                                        <span class="site-menu-title">Platform Hosts</span>
                                                    </a>
                                                </li>
                                                <li class="site-menu-item has-sub">
                                                    <a href="javascript:void(0)">
                                                        <span class="site-menu-title">Management Reports</span>
                                                        <span class="site-menu-arrow"></span>
                                                    </a>
                                                    <ul class="site-menu-sub">
                                                        <li class="site-menu-item">
                                                            <a class="animsition-link" href="/admin/management-report/current">
                                                                <span class="site-menu-title">Current State</span>
                                                            </a>
                                                        </li>
                                                        <li class="site-menu-item">
                                                            <a class="animsition-link" href="/admin/management-report/monthly-trend">
                                                                <span class="site-menu-title">Trend Monthly</span>
                                                            </a>
                                                        </li>
                                                        <li class="site-menu-item">
                                                            <a class="animsition-link" href="/admin/management-report/bimonthly-trend">
                                                                <span class="site-menu-title">Trend Bi-Monthly</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </li>
                                                <li class="site-menu-item has-sub">
                                                  <a href="javascript:void(0)">
                                                    <span class="site-menu-title">Subscription Reports</span>
                                                    <span class="site-menu-arrow"></span>
                                                  </a>
                                                  <ul class="site-menu-sub">
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/subscription-report/user-dash">
                                                        <span class="site-menu-title">User Dashboard</span>
                                                      </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/subscription-report/agency-dash">
                                                        <span class="site-menu-title">Agency Dashboard</span>
                                                      </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/subscription-report/wavo3">
                                                        <span class="site-menu-title">Wavo 3.0</span>
                                                      </a>
                                                    </li>
                                                    <li class="site-menu-item">
                                                      <a class="animsition-link" href="/admin/subscription-report/wavo3e">
                                                        <span class="site-menu-title">Wavo 3.0 EC</span>
                                                      </a>
                                                    </li>
                                                  </ul>
                                                </li>
                                                <li class="site-menu-item has-sub">
                                                    <a href="javascript:void(0)">
                                                        <span class="site-menu-title">Data Lists</span>
                                                        <span class="site-menu-arrow"></span>
                                                    </a>
                                                    <ul class="site-menu-sub">
                                                        <li class="site-menu-item">
                                                            <a class="animsition-link" href="/admin/campaign/geographies">
                                                                <span class="site-menu-title">Geographies</span>
                                                            </a>
                                                        </li>
                                                        <li class="site-menu-item">
                                                            <a class="animsition-link" href="/admin/campaign/industries">
                                                                <span class="site-menu-title">Industries</span>
                                                            </a>
                                                        </li>
                                                        <li class="site-menu-item">
                                                            <a class="animsition-link" href="/admin/campaign/company_sizes">
                                                                <span class="site-menu-title">Company Sizes</span>
                                                            </a>
                                                        </li>
                                                        <li class="site-menu-item">
                                                            <a class="animsition-link" href="/admin/campaign/job_functions">
                                                                <span class="site-menu-title">Job Functions</span>
                                                            </a>
                                                        </li>
                                                        <li class="site-menu-item">
                                                            <a class="animsition-link" href="/admin/campaign/seniorities">
                                                                <span class="site-menu-title">Seniorities</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </li>
                                                @can('demo')
                                                    <!-- Demos -->
                                                    <li class="site-menu-item has-sub">
                                                        <a href="javascript:void(0)">
                                                            <span class="site-menu-title">Demos</span>
                                                            <span class="site-menu-arrow"></span>
                                                        </a>
                                                        <ul class="site-menu-sub">
                                                            <li class="site-menu-item">
                                                                <a class="animsition-link" href="/prospects-demo">
                                                                    <span class="site-menu-title">Contacts Demo</span>
                                                                </a>
                                                            </li>
                                                            <li class="site-menu-item">
                                                                <a class="animsition-link" href="/stats-demo">
                                                                    <span class="site-menu-title">Stats Demo</span>
                                                                </a>
                                                            </li>
                                                            @can('campaign.demo')
                                                                <li class="site-menu-item">
                                                                    <a class="animsition-link" href="/campaigns-demo">
                                                                        <span class="site-menu-title">Campaign Setup Demo</span>
                                                                    </a>
                                                                </li>
                                                            @endcan
                                                        </ul>
                                                    </li>
                                                @endcan
                                                @can('horizon')
                                                    <!-- Horizon -->
                                                    <li class="site-menu-item">
                                                        <a class="animsition-link" href="/horizon" target="_blank">
                                                            <i class="site-menu-icon wb-dashboard d-none" aria-hidden="true"></i>
                                                            <span class="site-menu-title">Horizon</span>
                                                        </a>
                                                    </li>
                                                @endcan
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    @endcan

                </ul>
            </div>
        </div>
    </div>
</div>
