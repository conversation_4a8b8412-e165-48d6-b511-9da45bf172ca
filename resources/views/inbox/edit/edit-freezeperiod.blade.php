<div class="panel panel-bordered">  
	<div class="panel-body">
		<div class="px-15">
			<div class="row mb-20">
				<div class="col-9 col-sm-9 col-lg-6 col-xl-7 col-md-7">
					<h4 class="pt-5 mt-3 text-truncate">
						Freeze Periods 
						<span class="" data-toggle="tooltip" data-placement="top" 
							id="scheduleFreezeTooltip"
                      		title="No emails will be sent from this account during scheduled Freeze Periods. All campaigns will pick up where they left off after the Freeze Period."
                      	>
							<i class="wb-info-circle"></i>	
						</span>
					</h4>
				</div>
				<div class="col-3 col-sm-3 col-lg-6 col-xl-5 col-md-5 text-right">
					<button type="button" class="btn btn-primary" 
						data-toggle="modal" data-target="#scheduleFreezeModal"
					>
						<i class="icon wb-plus hidden-md"></i> 
						<span class="hidden-sm-down">
							Add Freeze Period
						</span>  
					</button>
				</div>
			</div>  
		</div>

		
		<div v-if="freezeSchedules.length" class="px-15">
			<div class="card card-shadow mb-3 border" v-for="(schedule, index) in freezeSchedules">
				<div class="card-block p-15">
					<div class="row">
						<div class="col-9 col-sm-10 col-xl-8 col-md-8 col-lg-8">
							<h4 class="m-0 text-truncate"> 
								@{{schedule.tz_start}} - @{{schedule.tz_end}}
								<small class="d-block pt-5 text-capitalize"> 
									@{{schedule.timezone.replace(/_/gi, ' ')}}
								</small>
							</h4>
						</div>
						<div class="col-3 col-sm-2 col-md-4 col-lg-4 col-xl-4 text-right pt-3"> 
							<button class="btn btn-danger" @click="removeEmailFreeze(schedule)">  
								<i class="icon wb-close hidden-md"></i> 
								<span class="hidden-sm-down w-50 d-inline-block">
									Remove
								</span>  
							</button>
						</div>
					</div>
				</div> 
			</div>   
		</div>
		<div v-else>   
			<div class="px-15">
				<div class="alert alert-warning alert-icon"> 
					<i class="wb-alert-circle icon"></i>
					There are no freeze periods for this email account.
				</div>
			</div>
		</div> 
	</div>
</div>


<!-- Modal -->
<div class="modal fade" id="scheduleFreezeModal" 
	aria-hidden="false" 
	aria-labelledby="scheduleFreezeModalLabel"
	role="dialog"
>
	<div class="modal-dialog modal-simple">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">×</span>
				</button>
				<h4 class="modal-title" id="exampleFormModalLabel">
					Add Email Freeze Period 
				</h4>
			</div>
			<div class="modal-body pt-20">
				<div class="alert-icon alert alert-primary">
					<i class="icon fa fa-calendar-times-o"></i>
					Schedule days where no emails will be sent from this email account.
				</div>

				<div class="row"> 
					<div class="col-12 mb-20">
						<label class="d-block">
							Dates
							<span class="grey-500">*</span>
						</label>
						<div class="input-daterange input-group" id="datepicker">
	    					<span class="input-group-addon">From</span>
							<input type="text" class="form-control px-5" id="freezeStart" name="start" />
							<span class="input-group-addon">To</span>
							<input type="text" class="form-control px-5" id="freezeEnd" />
						</div>
					</div>
					<div class="col-12 mb-20">
						<label class="d-block">
							Timezone
							<span class="grey-500">*</span>
						</label> 
						<select-timezone 
							class="form-control" 
							name="timezone" 
							v-model="newFreezeSched.timezone"
							@input="timezoneInput"
						></select-timezone> 
					</div>
					<div class="col-12 text-center"> 
						<button class="btn btn-icon btn-primary px-30" 
							@click="addNewEmailFreeze"
							:disabled="isAddingFreeze"
						>
							<span v-if="isAddingFreeze">
								<i class="fa fa-spinner fa-spin"></i> Adding
							</span>
							<span v-else>
								<i class="wb-plus "></i> Add Freeze Period
							</span>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- End Modal -->