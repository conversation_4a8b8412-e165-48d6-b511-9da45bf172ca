<div class="tabbed-inbox-scrollable tabbed-inbox-scrollable-content">
    <div class="list-group-item px-20" v-for="(cursorDomain, index) in cursorDomains.data">
        <div class="row">
            {{--
            <div class="col-lg-3 col-md-4 col-sm-1 col-1 cp">
                <div class="d-flex align-items-center">
                    <div @click="showDomain(cursorDomain)" class="text-ellipsis hidden-sm-down">
                        <p class="m-0">@{{ cursorDomain.name }}</p>
                    </div>
                </div>
            </div>
            --}}
            <div class="col-lg-4 col-md-5 col-sm-7 col-6 cp"  @click="showDomain(cursorDomain)">
                <p class="text-ellipsis m-0 vtop">
                    <span>
                        <img :src="cursorDomain.icon"
                            v-if="cursorDomain.icon"
                            alt="" class="search-company-thumbnail"
                            onerror="javascript:this.src='/img/icon-bg.png'"
                        >
                        <span v-else class="search-company-initial">
                            @{{cursorDomain.merchant_name?.[0]}}
                        </span>
                    </span>
                    <a :href="'//'+cursorDomain.name" target="_blank">@{{cursorDomain.merchant_name ?? cursorDomain.name}}</a>
                </p>
            </div>
            <div class="col-lg-8 col-md-7 col-sm-5 col-6">
                <div class="row">
                    <div class="col-lg-3 col-md-6 col-sm-12 col-12 cp" @click="showDomain(cursorDomain)">
                        <p class="text-ellipsis m-0 vtop">
                            @{{getDomainCategory(cursorDomain)}}
                        </p>
                    </div>
                    <div class="col-lg-3 col-md-6 hidden-sm-down cp" @click="showDomain(cursorDomain)">
                        <p class="text-ellipsis m-0 vtop">
                            @{{getDomainLocation(cursorDomain)}}
                        </p>
                    </div>
                    <div class="col-lg-3 hidden-md-down cp" @click="showDomain(cursorDomain)">
                        <p class="text-ellipsis m-0 vtop">
                            @{{getDomainPlatformName(cursorDomain)}}
                        </p>
                    </div>
                    <div class="col-lg-3 hidden-md-down cp" @click="showDomain(cursorDomain)">
                        <p class="text-ellipsis m-0 vtop">
                            @{{cursorDomain.estimated_sales?.name || '--'}}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
