@extends('layouts.app')

@section('page-title', 'LinkedIn Account: ' . $linkedinAccount->name)

@section('page-styles')
    <style>
        .li-account-avatar {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 45px;
            height: 45px;
            border-radius: 50%;
        }

        .li-account-avatar-card {
            width: 70px;
            height: 70px;
            border-radius: 50%;
        }

        .liaccount-activity-label {
            width: 120px;
        }

    </style>
@endsection

@section('content-layout')
<linkedin-account-show
    :account="{{$linkedinAccount}}"
    :duplicates="{{json_encode($duplicateAccounts)}}"
    inline-template
>
    <div class="page loader-wrapper" >
        <div class="page-header page-header-bordered">
            <h1 class="page-title">LinkedIn Account</h1>
            <p class="page-description m-0">
                https://www.linkedin.com{{$linkedinAccount->account_url}}
            </p>
            <div class="page-header-actions">
                <a href="{{ route('linkedin-accounts.activity', $linkedinAccount) }}"  class="btn btn-outline btn-default btn-round"  >
                    <span class="text hidden-sm-down">Activity</span>
                    <i class="icon wb-more-vertical" aria-hidden="true"></i>
                </a>
              @if (empty($linkedinAccount->cancelled_at))
                <a href="{{ route('linkedin-accounts.edit', $linkedinAccount) }}" class="btn btn-outline btn-primary btn-round"  >
                    <span class="text hidden-sm-down">Edit</span>
                    <i class="icon wb-edit" aria-hidden="true"></i>
                </a>
                <a href="{{ route('linkedin-accounts.reauth', $linkedinAccount) }}" class="btn btn-outline btn-danger btn-round"  >
                    <span class="text hidden-sm-down">Re-authenticate</span>
                    <i class="icon wb-lock" aria-hidden="true"></i>
                </a>
                <a href="{{ route('linkedin-accounts.delete', $linkedinAccount) }}" class="btn btn-outline btn-danger btn-round ml-20" >
                    <span class="text hidden-sm-down">Delete</span>
                    <i class="icon wb-minus-circle" aria-hidden="true"></i>
                </a>
              @else
                <a href="{{ route('linkedin-accounts.reauth', $linkedinAccount) }}"  class="btn btn-outline btn-danger btn-round"  >
                  <span class="text hidden-sm-down">Re-activate</span>
                  <i class="icon wb-reload" aria-hidden="true"></i>
                </a>
              @endif
            </div>
        </div>

        <div class="page-content container-fluid">
            @if($linkedinAccount->cancelled_at)
              <div class="alert alert-icon alert-danger bg-red-100 mt-10">
                <i class="icon wb-alert-circle"></i>
                <p class="mb-0"><strong>LinkedIn Account Cancelled</strong></p>
                <p class="mb-20">
                  This account is Cancelled, do you want to re-activate?
                </p>
                <p>
                  <a href="{{ route('linkedin-accounts.reauth', $linkedinAccount) }}" class="btn btn-danger" >
                    <i class="icon wb-reload" aria-hidden="true"></i> Re-Activate
                  </a>
                </p>
              </div>
            @endif
            @if (session('status'))
                <div class="mb-20 alert-dismissible alert alert-{{session('status') == 'success' ? 'success' : 'danger'}}">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ session('msg') }}
                </div>
            @endif

            @if (session('ajaxMsg'))
                <div class="mb-20 alert alert-dismissible alert-success">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ session('ajaxMsg') }}
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="mb-20" v-if="duplicateAccounts.length">
                <div class="alert alert-icon alert-danger mb-5"
                    v-for="duplicateAccount in duplicateAccounts"
                >
                    <i class="wb-warning icon"></i>
                    <a :href="'/linkedin-accounts/'+duplicateAccount.hashid"
                        class="btn btn-xs btn-danger text-white px-20 float-right  w-70"
                    >View</a>
                    <span>
                        <strong>Duplicate Account :</strong>
                        @{{duplicateAccount.name}}
                    </span>
                </div>
            </div>

            @include('linkedin-accounts.show.status')

            @include('linkedin-accounts.show.info')

            @include('linkedin-accounts.show.dates')

            @include('linkedin-accounts.show.limits')

            <div class="row">
                <div class="col-md-12">
                    <div class="panel  ">
                        <div class="panel-body">
                            <div class="row">
                                @if($displayProxy)
                                <div class="col-md-4 col-lg-3">
                                    <div class="text-center">
                                        <div class="counter-text text-truncate">
                                            @if($linkedinAccount->proxy)
                                                {{$linkedinAccount->proxy->name}}
                                            @else
                                                No Proxy
                                            @endif
                                        </div>
                                        <div class="font-size-10">
                                            PROXY
                                        </div>
                                    </div>
                                </div>
                                @endif
                                <div class="@if($displayProxy) col-md-8 col-lg-9 @else col-md-12 col-lg-12 @endif">
                                    <div class="text-center">
                                        <div class="counter-text text-truncate">
                                            {{$linkedinAccount->cookie ?? 'N/A'}}
                                        </div>
                                        <div class="font-size-10">
                                            COOKIE
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            @include('linkedin-accounts.show.challenge')

        </div>
    </div>
</linkedin-account-show>
@endsection
