<?php

namespace Laravel\Spark\Http\Requests\Settings\Teams\Subscription;

use <PERSON><PERSON>\Spark\Spark;
use <PERSON><PERSON>\Spark\Http\Requests\ValidatesBillingAddresses;
use <PERSON><PERSON>\Spark\Contracts\Http\Requests\Settings\Teams\Subscription\CreateSubscriptionRequest as Contract;

class CreateStripeSubscriptionRequest extends CreateSubscriptionRequest implements Contract
{
    use ValidatesBillingAddresses;

    /**
     * Get the validator for the request.
     *
     * @return \Illuminate\Validation\Validator
     */
    public function validator()
    {
        $validator = $this->baseValidator([
            'stripe_token' => 'required',
            'vat_id' => 'nullable|max:50|vat_id',
        ]);

        if (Spark::collectsBillingAddress()) {
            $this->validateBillingAddress($validator);
        }

        return $validator;
    }
}
