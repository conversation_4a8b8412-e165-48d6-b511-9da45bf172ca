{"private": true, "scripts": {"dev": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --watch --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "dependencies": {"bootstrap": "^3.0.0", "jquery": "^2.1.4", "js-cookie": "^2.1.0", "cross-env": "^3.2.3", "laravel-mix": "0.*", "moment": "^2.10.6", "promise": "^7.1.1", "sweetalert": "^1.1.3", "underscore": "^1.8.3", "urijs": "^1.17.0", "vue": "2.*", "axios": "^0.15.2"}}