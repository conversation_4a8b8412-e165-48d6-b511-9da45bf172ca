apiVersion: certmanager.k8s.io/v1alpha1
kind: ClusterIssuer
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"certmanager.k8s.io/v1alpha1","kind":"ClusterIssuer","metadata":{"annotations":{},"name":"staging-letsencrypt-issuer","namespace":""},"spec":{"acme":{"email":"<EMAIL>","http01":{},"privateKeySecretRef":{"name":"staging-letsencrypt-issuer"},"server":"https://acme-v02.api.letsencrypt.org/directory"}}}
  creationTimestamp: "2019-07-30T14:19:10Z"
  generation: 2
  name: staging-letsencrypt-issuer
  resourceVersion: "*********"
  uid: ffbd67aa-b2d4-11e9-b7d3-42010a8e0102
spec:
  acme:
    email: <EMAIL>
    http01: {}
    privateKeySecretRef:
      name: staging-letsencrypt-issuer
    server: https://acme-v02.api.letsencrypt.org/directory
status:
  acme:
    lastRegisteredEmail: <EMAIL>
    uri: https://acme-v02.api.letsencrypt.org/acme/acct/********
  conditions:
  - lastTransitionTime: "2019-07-30T14:19:11Z"
    message: The ACME account was registered with the ACME server
    reason: ACMEAccountRegistered
    status: "True"
    type: Ready
