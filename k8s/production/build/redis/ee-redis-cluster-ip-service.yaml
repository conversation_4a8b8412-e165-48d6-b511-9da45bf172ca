apiVersion: v1
kind: Service
metadata:
  labels:
    component: redis-cluster-ip
  name: production-ee-redis-cluster-ip-service
  namespace: wavo-production
  annotations:
    prometheus.io/scrape: 'true'
    prometheus.io/port: "9121"
spec:
  type: ClusterIP
  clusterIP: None
  selector:
    component: production-ee-redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
      name: production-ee-redis
    - protocol: TCP
      port: 9121
      targetPort: 9121
      name: redis-exporter