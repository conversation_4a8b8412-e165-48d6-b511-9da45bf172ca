apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: elastic-cluster
spec:
  version: 7.5.1
  nodeSets:
    - name: default120
      count: 3
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 120Gi
            storageClassName: faster
      config:
        node.master: true
        node.data: true
        node.ingest: true
      podTemplate:
        spec:
          tolerations:
            - key: cloud.google.com/gke-preemptible
              operator: Equal
              value: "true"
              effect: NoSchedule
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: elastic-node
                        operator: In
                        values:
                          - "true"
          initContainers:
            - name: sysctl
              securityContext:
                privileged: true
              command: ['sh', '-c', 'sysctl -w vm.max_map_count=262144']
          containers:
            - name: elasticsearch
              env:
                - name: ES_JAVA_OPTS
                  value: -Xms1g -Xmx1g
              resources:
                requests:
                  memory: 2Gi
                  cpu: 0.5
                limits:
                  memory: 2Gi
                  cpu: 0.75
