apiVersion: batch/v1
kind: CronJob
metadata:
  name: staging-schedule-cron-job
  namespace: wavo-staging
spec:
  schedule: "*/5 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                    - key: node-group
                      operator: In
                      values:
                        - "default"
                    - key: node-group
                      operator: NotIn
                      values:
                        - "cheap"
                        - "redis"
                        - "browser"
                        - "elastic"
                        - "emailengine"
          volumes:
            - name: google-cloud-storage-key
              secret:
                secretName: staging-storage
            - name: stackdriver-key
              secret:
                secretName: staging-stackdriver-sa
          containers:
            - name: staging-schedule-cron-job
              image: gcr.io/wavo-225922/wavo-app:v3-dev-latest
              imagePullPolicy: Always
              command: ["php","/var/www/html/artisan", "schedule:run"]
              envFrom:
                - configMapRef:
                    name: staging-env-config
                - secretRef:
                    name: staging-app-secret
              volumeMounts:
                - name: google-cloud-storage-key
                  mountPath: /var/secrets/google
                - name: stackdriver-key
                  mountPath: /var/secrets/stackdriver
              env:
                - name: GOOGLE_CLOUD_KEY_FILE
                  value: /var/secrets/google/key.json
                - name: STACKDRIVER_KEY_FILE
                  value: /var/secrets/stackdriver/key.json
                - name: START_FRONTEND
                  value: "false"
                - name: START_HORIZON
                  value: "false"
