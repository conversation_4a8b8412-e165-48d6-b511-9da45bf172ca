## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Email Messages'
description: |-

  APIs for managing email messages.
endpoints:
  -
    httpMethods:
      - GET
    uri: 'api/v1/campaigns/{campaign_id}/replies'
    metadata:
      groupName: 'Email Messages'
      groupDescription: |-

        APIs for managing email messages.
      subgroup: ''
      subgroupDescription: ''
      title: "Display a listing of a campaign's replies."
      description: 'Return all email message replies of a campaign.'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      campaign_id:
        name: campaign_id
        description: 'The id of a campaign.'
        required: true
        example: fkjtf4390kfgu8903nsk
        type: string
        custom: []
    cleanUrlParameters:
      campaign_id: fkjtf4390kfgu8903nsk
    queryParameters:
      limit:
        name: limit
        description: 'The number of objects to return. Defaults to 10. Maximum 20.'
        required: false
        example: 2
        type: integer
        custom: []
      offset:
        name: offset
        description: 'The zero-based offset for the default object sorting.'
        required: false
        example: '0'
        type: string
        custom: []
    cleanQueryParameters:
      limit: 2
      offset: '0'
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
              "data": [
                  {
                      "id": "2dpkq21yvjn4r530y56gzwlm3e08ox",
                      "thread_id": "l12zpqxdn36mwrog08e5mwrkj",
                      "contact_id": "83jm7zlgve0nlzq4d15orxywn",
                      "from_email": "<EMAIL>",
                      "to_email": "<EMAIL>",
                      "from_name": "John Jackson",
                      "to_name": "Corwin Velda",
                      "submitted_at": 1597066351,
                      "created_at": 1597066376,
                      "subject": "Re: Test message subject",
                      "snippet": "Short Message Body...",
                      "message_body": "Message body text...",
                      "message_raw": "Raw Message text with html tags..."
                  },
                  {
                      "id": "66lgem812pz0w7y6g9yoj3nr4kvqdx",
                      "thread_id": "rdle34wnjy94qj6m178p0gz5v",
                      "contact_id": "8j5orm17lnqgpkqvg9pk4z3ew",
                      "from_email": "<EMAIL>",
                      "to_email": "<EMAIL>",
                      "from_name": "Ella Jones",
                      "to_name": "Corwin Velda",
                      "submitted_at": 1597066351,
                      "created_at": 1597066376,
                      "subject": "Re: Test message subject",
                      "snippet": "Short Message Body...",
                      "message_body": "Message body text...",
                      "message_raw": "Raw Message text with html tags..."
                  }
              ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/contacts/{contact_id}/replies'
    metadata:
      groupName: 'Email Messages'
      groupDescription: |-

        APIs for managing email messages.
      subgroup: ''
      subgroupDescription: ''
      title: "Display a listing of a contact's replies."
      description: 'Return all email message replies of a contact.'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      contact_id:
        name: contact_id
        description: 'The id of a contact.'
        required: true
        example: 83jm7zlgve0nlzq4d15orxywn
        type: string
        custom: []
    cleanUrlParameters:
      contact_id: 83jm7zlgve0nlzq4d15orxywn
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
              "data": [
                  {
                      "id": "2dpkq21yvjn4r530y56gzwlm3e08ox",
                      "thread_id": "l12zpqxdn36mwrog08e5mwrkj",
                      "contact_id": "83jm7zlgve0nlzq4d15orxywn",
                      "from_email": "<EMAIL>",
                      "to_email": "<EMAIL>",
                      "from_name": "John Jackson",
                      "to_name": "Corwin Velda",
                      "submitted_at": 1597066351,
                      "created_at": 1597066376,
                      "subject": "Re: Test message subject",
                      "snippet": "Short Message Body...",
                      "message_body": "Message body text...",
                      "message_raw": "Raw Message text with html tags..."
                  },
                  {
                      "id": "rgr8yd4lkzvp05dw29nxe1w62m3qoj",
                      "thread_id": "l12zpqxdn36mwrog08e5mwrkj",
                      "contact_id": "83jm7zlgve0nlzq4d15orxywn",
                      "from_email": "<EMAIL>",
                      "to_email": "<EMAIL>",
                      "from_name": "John Jackson",
                      "to_name": "Corwin Velda",
                      "submitted_at": 1597066499,
                      "created_at": 1597066528,
                      "subject": "Re: Test message subject",
                      "snippet": "Short Message Body...",
                      "message_body": "Message body text...",
                      "message_raw": "Raw Message text with html tags..."
                  }
              ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/email-threads/{email_thread_id}'
    metadata:
      groupName: 'Email Messages'
      groupDescription: |-

        APIs for managing email messages.
      subgroup: ''
      subgroupDescription: ''
      title: 'Display an email thread.'
      description: 'Return an email thread with all its messages.'
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      email_thread_id:
        name: email_thread_id
        description: 'The id of the email thread.'
        required: true
        example: rdle34wnjy94qj6m178p0gz5v
        type: string
        custom: []
    cleanUrlParameters:
      email_thread_id: rdle34wnjy94qj6m178p0gz5v
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
              "data": {
                  "id": "rdle34wnjy94qj6m178p0gz5v",
                  "campaign_id": "jxv8qkeml4ml1g69y3w5",
                  "contact_id": "8j5orm17lnqgpkqvg9pk4z3ew",
                  "email_messages": [
                      {
                          "id": "2dpkq21yvjn4r530y56gzwlm3e08ox",
                          "thread_id": "l12zpqxdn36mwrog08e5mwrkj",
                          "contact_id": "83jm7zlgve0nlzq4d15orxywn",
                          "from_email": "<EMAIL>",
                          "to_email": "<EMAIL>",
                          "from_name": "Corwin Velda",
                          "to_name": "Ella Jones",
                          "submitted_at": 1597066219,
                          "created_at": 1597066208,
                          "subject": "Test message subject",
                          "snippet": "Short Message Body...",
                          "message_body": "Message body text...",
                          "message_raw": "Raw Message text with html tags..."
                      },
                      {
                          "id": "66lgem812pz0w7y6g9yoj3nr4kvqdx",
                          "thread_id": "rdle34wnjy94qj6m178p0gz5v",
                          "contact_id": "8j5orm17lnqgpkqvg9pk4z3ew",
                          "from_email": "<EMAIL>",
                          "to_email": "<EMAIL>",
                          "from_name": "Ella Jones",
                          "to_name": "Corwin Velda",
                          "submitted_at": 1597066351,
                          "created_at": 1597066376,
                          "subject": "Re: Test message subject",
                          "snippet": "Short Message Body...",
                          "message_body": "Message body text...",
                          "message_raw": "Raw Message text with html tags..."
                       }
                   ]
              }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer token'
    controller: null
    method: null
    route: null
    custom: []
