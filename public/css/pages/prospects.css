div.dataTables_wrapper div.dataTables_filter {
    text-align: left;
}
.dataTable tbody tr {
    cursor: pointer;
}

.loading-overlay {
    background-color: rgba(0,0,0,.1);
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10000;
    height: 100%;
}

label {
    margin-bottom: 0;
}

#campaign-select .bootstrap-select > .dropdown-toggle.bs-placeholder {
    color: #fff !important;
    font-weight: 500;
}
#campaign-select .bootstrap-select > .dropdown-toggle {
    font-weight: 500;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder {
    color: #76838f;
    font-weight: 400;
}
.bootstrap-select > .dropdown-toggle {
    font-weight: 400;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder:hover,
.bootstrap-select > .dropdown-toggle.bs-placeholder:active,
.bootstrap-select > .dropdown-toggle.bs-placeholder:focus {
    color: #fff;
}